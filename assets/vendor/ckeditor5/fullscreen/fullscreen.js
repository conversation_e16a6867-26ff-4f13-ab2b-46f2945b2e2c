!function(e){const t=e.en=e.en||{};t.dictionary=Object.assign(t.dictionary||{},{"Connected users":"Connected users","Document outline":"Document outline","Enter fullscreen mode":"Enter fullscreen mode","Fullscreen mode":"Fullscreen mode","Leave fullscreen mode":"Leave fullscreen mode","Toggle fullscreen mode":"Toggle fullscreen mode"})}(window.CKEDITOR_TRANSLATIONS||(window.CKEDITOR_TRANSLATIONS={})),
/*!
 * @license Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.
 * For licensing, see LICENSE.md.
 */(()=>{var e={21:e=>{"use strict";e.exports=function(e,t){Object.keys(t).forEach((function(i){e.setAttribute(i,t[i])}))}},51:e=>{"use strict";e.exports=function(e){var t=document.createElement("style");return e.setAttributes(t,e.attributes),e.insert(t,e.options),t}},128:e=>{"use strict";var t={};e.exports=function(e,i){var n=function(e){if(void 0===t[e]){var i=document.querySelector(e);if(window.HTMLIFrameElement&&i instanceof window.HTMLIFrameElement)try{i=i.contentDocument.head}catch(e){i=null}t[e]=i}return t[e]}(e);if(!n)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");n.appendChild(i)}},237:e=>{"use strict";e.exports=CKEditor5.dll},311:(e,t,i)=>{e.exports=i(237)("./src/ui.js")},355:(e,t,i)=>{e.exports=i(237)("./src/icons.js")},584:(e,t,i)=>{e.exports=i(237)("./src/utils.js")},591:e=>{"use strict";var t=[];function i(e){for(var i=-1,n=0;n<t.length;n++)if(t[n].identifier===e){i=n;break}return i}function n(e,n){for(var s={},o=[],l=0;l<e.length;l++){var c=e[l],a=n.base?c[0]+n.base:c[0],d=s[a]||0,u="".concat(a," ").concat(d);s[a]=d+1;var h=i(u),p={css:c[1],media:c[2],sourceMap:c[3],supports:c[4],layer:c[5]};if(-1!==h)t[h].references++,t[h].updater(p);else{var f=r(p,n);n.byIndex=l,t.splice(l,0,{identifier:u,updater:f,references:1})}o.push(u)}return o}function r(e,t){var i=t.domAPI(t);i.update(e);return function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap&&t.supports===e.supports&&t.layer===e.layer)return;i.update(e=t)}else i.remove()}}e.exports=function(e,r){var s=n(e=e||[],r=r||{});return function(e){e=e||[];for(var o=0;o<s.length;o++){var l=i(s[o]);t[l].references--}for(var c=n(e,r),a=0;a<s.length;a++){var d=i(s[a]);0===t[d].references&&(t[d].updater(),t.splice(d,1))}s=c}}},639:e=>{"use strict";var t,i=(t=[],function(e,i){return t[e]=i,t.filter(Boolean).join("\n")});function n(e,t,n,r){var s;if(n)s="";else{s="",r.supports&&(s+="@supports (".concat(r.supports,") {")),r.media&&(s+="@media ".concat(r.media," {"));var o=void 0!==r.layer;o&&(s+="@layer".concat(r.layer.length>0?" ".concat(r.layer):""," {")),s+=r.css,o&&(s+="}"),r.media&&(s+="}"),r.supports&&(s+="}")}if(e.styleSheet)e.styleSheet.cssText=i(t,s);else{var l=document.createTextNode(s),c=e.childNodes;c[t]&&e.removeChild(c[t]),c.length?e.insertBefore(l,c[t]):e.appendChild(l)}}var r={singleton:null,singletonCounter:0};e.exports=function(e){if("undefined"==typeof document)return{update:function(){},remove:function(){}};var t=r.singletonCounter++,i=r.singleton||(r.singleton=e.insertStyleElement(e));return{update:function(e){n(i,t,!1,e)},remove:function(e){n(i,t,!0,e)}}}},758:e=>{"use strict";e.exports=function(e){return e[1]}},782:(e,t,i)=>{e.exports=i(237)("./src/core.js")},935:e=>{"use strict";e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var i="",n=void 0!==t[5];return t[4]&&(i+="@supports (".concat(t[4],") {")),t[2]&&(i+="@media ".concat(t[2]," {")),n&&(i+="@layer".concat(t[5].length>0?" ".concat(t[5]):""," {")),i+=e(t),n&&(i+="}"),t[2]&&(i+="}"),t[4]&&(i+="}"),i})).join("")},t.i=function(e,i,n,r,s){"string"==typeof e&&(e=[[null,e,void 0]]);var o={};if(n)for(var l=0;l<this.length;l++){var c=this[l][0];null!=c&&(o[c]=!0)}for(var a=0;a<e.length;a++){var d=[].concat(e[a]);n&&o[d[0]]||(void 0!==s&&(void 0===d[5]||(d[1]="@layer".concat(d[5].length>0?" ".concat(d[5]):""," {").concat(d[1],"}")),d[5]=s),i&&(d[2]?(d[1]="@media ".concat(d[2]," {").concat(d[1],"}"),d[2]=i):d[2]=i),r&&(d[4]?(d[1]="@supports (".concat(d[4],") {").concat(d[1],"}"),d[4]=r):d[4]="".concat(r)),t.push(d))}},t}},998:(e,t,i)=>{"use strict";i.d(t,{A:()=>l});var n=i(758),r=i.n(n),s=i(935),o=i.n(s)()(r());o.push([e.id,'body.ck-fullscreen,html.ck-fullscreen{--ck-z-fullscreen:10000;overflow:hidden;--ck-z-default:calc(var(--ck-z-fullscreen) + 1);--ck-z-panel:calc(var(--ck-z-default) + 999);--ck-z-dialog:100000}body.ck-fullscreen .ckbox-reset:not(#n)>.ckbox--dialog,html.ck-fullscreen .ckbox-reset:not(#n)>.ckbox--dialog{--ckbox-z-index-root:calc(var(--ck-z-dialog) + 1)}body.ck-fullscreen .ckbox:not(#n) .ckbox-img-editor,html.ck-fullscreen .ckbox:not(#n) .ckbox-img-editor{--ckbox-z-index-preview:calc(var(--ck-z-dialog) + 1)}body.ck-fullscreen .ck-pagination-view-line,html.ck-fullscreen .ck-pagination-view-line{z-index:calc(var(--ck-z-fullscreen) + 1)}body.ck-fullscreen .page-break__label,html.ck-fullscreen .page-break__label{z-index:calc(var(--ck-z-fullscreen) + 2)}.ck.ck-fullscreen__main-wrapper{background:var(--ck-color-base-foreground);display:flex;flex-direction:column;height:100%;left:0;position:fixed;top:0;width:100%;z-index:var(--ck-z-fullscreen)}.ck.ck-fullscreen__main-wrapper .ck.ck-revision-history-ui__changes-navigation{margin-bottom:0;margin-top:0}:not(body>.ck-fullscreen__main-wrapper).ck-fullscreen__main-wrapper{position:absolute}.ck-fullscreen__main-wrapper:not(body>.ck-fullscreen__main-wrapper) .ck-fullscreen__top-wrapper{border-left:1px solid var(--ck-color-base-border);border-radius:var(--ck-border-radius) 0;border-right:1px solid var(--ck-color-base-border);border-top:1px solid var(--ck-color-base-border)}.ck-fullscreen__menu-bar .ck.ck-menu-bar{border:none}.ck.ck-fullscreen__toolbar .ck-toolbar{border-left:0;border-radius:0;border-right:0}.ck-fullscreen__main-wrapper .ck-fullscreen__editable-wrapper{--ck-fullscreen-editor-top-margin:28px;--ck-fullscreen-editor-bottom-margin:28px;display:flex;justify-content:flex-start;max-height:100%;overflow:auto}.ck-fullscreen__main-wrapper .ck-fullscreen__editable{height:100%;margin-left:auto;margin-top:var(--ck-fullscreen-editor-top-margin)}.ck-fullscreen__main-wrapper .ck-fullscreen__editable:after{content:"";display:block;height:var(--ck-fullscreen-editor-bottom-margin)}.ck-fullscreen__main-wrapper .ck-fullscreen__editable .ck.ck-editor__editable:not(.ck-editor__nested-editable){background:#fff;border:1px solid var(--ck-color-base-border);box-shadow:0 2px 3px rgba(0,0,0,.078);box-sizing:border-box;height:fit-content;margin:0;max-width:210.52917mm;min-height:297mm;padding:20mm 12mm;width:210.52917mm}.ck-fullscreen__main-wrapper .ck-fullscreen__editable .ck-source-editing-area{width:210.52917mm}.ck-fullscreen__sidebar{margin-left:10px;margin-top:var(--ck-fullscreen-editor-top-margin);width:300px}.ck-fullscreen__left-sidebar{font-family:Helvetica,Arial,sans-serif;--ck-user-avatar-size:28px;align-self:flex-start;background-color:transparent;box-sizing:border-box;display:flex;flex-direction:column;height:100%;margin-top:0;position:sticky;top:0}.ck-fullscreen__left-sidebar>:first-child{min-width:300px;padding-top:var(--ck-fullscreen-editor-top-margin)}.ck-fullscreen__left-sidebar .ck.ck-presence-list--collapsed{--ck-user-avatar-size:32px}.ck-fullscreen__left-sidebar .ck-presence-list__users-counter__text,.ck-fullscreen__left-sidebar .ck-user{font-size:.85em}.ck-fullscreen__left-sidebar-item{margin-bottom:var(--ck-spacing-medium);padding:var(--ck-spacing-medium)}.ck-fullscreen__left-sidebar-item:first-child{padding-top:0}.ck-fullscreen__left-sidebar-item:last-child{margin-bottom:0}.ck-fullscreen__left-sidebar-header{--ck-fullscreen-presence-list-header-font-size:0.875em;color:var(--ck-document-outline-item-default-color);font-size:var(--ck-fullscreen-presence-list-header-font-size);font-weight:700;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.ck-fullscreen__left-sidebar--sticky{position:sticky;top:0}.ck-fullscreen__left-sidebar--sticky>:first-child{padding-top:0}.ck-fullscreen__presence-list{margin-top:var(--ck-spacing-medium)}.ck-fullscreen__left-sidebar-item--no-margin{margin:0}.ck-fullscreen__left-sidebar .ck.ck-document-outline{padding-left:0;padding-right:0;padding-top:0}.ck-fullscreen__document-outline-wrapper{overflow-y:auto;padding-top:0}.ck-fullscreen__sidebar.ck-fullscreen__right-sidebar{margin-right:auto;margin-top:var(--ck-fullscreen-editor-top-margin)}.ck-fullscreen__sidebar.ck-fullscreen__right-sidebar>:first-child{min-width:300px}',""]);const l=o}},t={};function i(n){var r=t[n];if(void 0!==r)return r.exports;var s=t[n]={id:n,exports:{}};return e[n](s,s.exports,i),s.exports}i.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return i.d(t,{a:t}),t},i.d=(e,t)=>{for(var n in t)i.o(t,n)&&!i.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},i.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),i.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};(()=>{"use strict";i.r(n),i.d(n,{AbstractEditorHandler:()=>s,Fullscreen:()=>x,FullscreenEditing:()=>a,FullscreenUI:()=>C});var e=i(782),t=i(584),r=i(311);class s{_placeholderMap;_wrapper=null;_document;_annotationsUIsData=null;_paginationBodyCollection=null;_sourceEditingCallback=(e,t,i)=>{this.getWrapper().querySelector(".ck-fullscreen__document-outline-header").style.display=i?"none":""};_hiddenElements=new Map;_savedAncestorsScrollPositions=new Map;_showRevisionViewerCallback=null;_closeRevisionViewerCallback=null;constructor(e){this._placeholderMap=new Map,e.plugins.has("RevisionHistory")&&(this._showRevisionViewerCallback=e.config.get("revisionHistory").showRevisionViewerCallback,this._closeRevisionViewerCallback=e.config.get("revisionHistory").closeRevisionViewerCallback),this._editor=e,this._document=this._editor.sourceElement?this._editor.sourceElement.ownerDocument:t.global.document,this._editor.config.define("fullscreen.container",this._document.body),e.on("destroy",(()=>{this._wrapper&&this.destroy()}))}moveToFullscreen(e,i){const n=(0,t.createElement)(this._document,"div");n.setAttribute("data-ck-fullscreen-placeholder",i),e.replaceWith(n),this.getWrapper().querySelector(`[data-ck-fullscreen="${i}"]`).append(e),this._placeholderMap.set(i,{placeholderElement:n,movedElement:e})}restoreMovedElementLocation(e){if(!this._placeholderMap.has(e))return;const{placeholderElement:t,movedElement:i}=this._placeholderMap.get(e);t.replaceWith(i),t.remove(),this._placeholderMap.delete(e),0===this._placeholderMap.size&&this._destroyContainer()}getWrapper(){return this._wrapper||(this._wrapper=(0,t.createElement)(this._document,"div",{class:"ck ck-fullscreen__main-wrapper"}),this._wrapper.innerHTML='\n\t\t\t\t<div class="ck ck-fullscreen__top-wrapper ck-reset_all">\n\t\t\t\t\t<div class="ck ck-fullscreen__menu-bar" data-ck-fullscreen="menu-bar"></div>\n\t\t\t\t\t<div class="ck ck-fullscreen__toolbar" data-ck-fullscreen="toolbar"></div>\n\t\t\t\t</div>\n\t\t\t\t<div class="ck ck-fullscreen__editable-wrapper">\n\t\t\t\t\t<div class="ck ck-fullscreen__sidebar ck-fullscreen__left-sidebar" data-ck-fullscreen="left-sidebar"></div>\n\t\t\t\t\t<div class="ck ck-fullscreen__editable" data-ck-fullscreen="editable">\n\t\t\t\t\t\t<div class="ck ck-fullscreen__pagination-view" data-ck-fullscreen="pagination-view"></div>\n\t\t\t\t\t</div>\n\t\t\t\t\t<div class="ck ck-fullscreen__sidebar ck-fullscreen__right-sidebar" data-ck-fullscreen="right-sidebar"></div>\n\t\t\t\t</div>\n\t\t\t\t<div class="ck ck-fullscreen__bottom-wrapper">\n\t\t\t\t\t<div class="ck ck-fullscreen__body-wrapper" data-ck-fullscreen="body-wrapper"></div>\n\t\t\t\t</div>\n\t\t\t',this._editor.config.get("fullscreen.container").appendChild(this._wrapper)),this._wrapper}enable(){if(this._saveAncestorsScrollPositions(this._editor.ui.getEditableElement()),this.defaultOnEnter(),this._editor.config.get("fullscreen.container")===this._document.body&&(this._document.body.classList.add("ck-fullscreen"),this._document.body.parentElement.classList.add("ck-fullscreen")),this._editor.plugins.has("Dialog")&&this._registerFullscreenDialogPositionAdjustments(),this._editor.plugins.has("PresenceListUI")&&this._generatePresenceListContainer(),this._editor.plugins.has("DocumentOutlineUI")&&this._generateDocumentOutlineContainer(),this._editor.plugins.has("Pagination")&&this._editor.plugins.get("Pagination").isEnabled){const e=this._editor.plugins.get("PaginationRenderer");e.setupScrollableAncestor(),this._paginationBodyCollection=new r.BodyCollection(this._editor.locale),this._paginationBodyCollection.attachToDom(),e.linesRepository.setViewCollection(this._paginationBodyCollection),this._editor.once("destroy",(()=>{this._paginationBodyCollection.detachFromDom()})),this.moveToFullscreen(this._paginationBodyCollection.bodyCollectionContainer,"body-wrapper")}this._editor.plugins.has("AnnotationsUIs")&&this._overrideAnnotationsUIs(),this._editor.plugins.has("RevisionHistory")&&(this._editor.plugins.get("RevisionHistory").isRevisionViewerOpen&&this._editor.config.get("revisionHistory.closeRevisionViewerCallback")(),this._overrideRevisionHistoryCallbacks()),this._editor.plugins.has("SourceEditing")&&this._editor.plugins.has("DocumentOutlineUI")&&this._editor.plugins.get("SourceEditing").on("change:isSourceEditingMode",this._sourceEditingCallback);for(const e of this._editor.config.get("fullscreen.container").children)e===this._wrapper||e.classList.contains("ck-body-wrapper")||e.classList.contains("ckbox-wrapper")||"none"===e.style.display||(this._hiddenElements.set(e,e.style.display),e.style.display="none");this._editor.config.get("fullscreen.onEnterCallback")&&this._editor.config.get("fullscreen.onEnterCallback")(this.getWrapper())}disable(){this._editor.config.get("fullscreen.onLeaveCallback")&&this._editor.config.get("fullscreen.onLeaveCallback")(this.getWrapper()),this._document.body.classList.remove("ck-fullscreen"),this._document.body.parentElement.classList.remove("ck-fullscreen"),this._editor.plugins.has("DocumentOutlineUI")&&this._restoreDocumentOutlineDefaultContainer(),this._annotationsUIsData&&this._restoreAnnotationsUIs(),this._editor.plugins.has("RevisionHistory")&&this._restoreRevisionHistoryCallbacks(),this._editor.plugins.has("SourceEditing")&&this._editor.plugins.has("DocumentOutlineUI")&&this._editor.plugins.get("SourceEditing").off("change:isSourceEditingMode",this._sourceEditingCallback);for(const e of this._placeholderMap.keys())this.restoreMovedElementLocation(e);this._destroyContainer(),this._editor.ui.view.toolbar&&this._editor.ui.view.toolbar.switchBehavior(!0===this._editor.config.get("toolbar.shouldNotGroupWhenFull")?"static":"dynamic");for(const[e,t]of this._savedAncestorsScrollPositions)e.scrollTo({left:t.scrollLeft,top:t.scrollTop,behavior:"instant"});if(this._savedAncestorsScrollPositions.clear(),this._editor.plugins.has("Pagination")&&this._editor.plugins.get("Pagination").isEnabled){const e=this._editor.plugins.get("PaginationRenderer");e.setupScrollableAncestor(),e.linesRepository.setViewCollection(this._editor.ui.view.body),this._paginationBodyCollection.detachFromDom(),this._paginationBodyCollection?.destroy()}this._editor.plugins.has("Dialog")&&this._unregisterFullscreenDialogPositionAdjustments()}destroy(){for(const{placeholderElement:e,movedElement:t}of this._placeholderMap.values())e.remove(),t.remove();this._destroyContainer(),this._document.body.classList.remove("ck-fullscreen"),this._document.body.parentElement.classList.remove("ck-fullscreen")}defaultOnEnter(){return this.getWrapper()}_destroyContainer(){if(this._wrapper){this._wrapper.remove(),this._wrapper=null;for(const[e,t]of this._hiddenElements)e.style.display=t;this._hiddenElements.clear()}}_generatePresenceListContainer(){const e=this._editor.t,i=(0,t.createElement)(document,"div",{class:"ck ck-fullscreen__left-sidebar-item"});i.innerHTML='\n\t\t\t<div class="ck ck-fullscreen__left-sidebar-header"></div>\n\t\t\t<div class="ck ck-fullscreen__presence-list" data-ck-fullscreen="presence-list"></div>\n\t\t',i.firstElementChild.innerText=e("Connected users"),document.querySelector('[data-ck-fullscreen="left-sidebar-sticky"]')||document.querySelector('[data-ck-fullscreen="left-sidebar"]').appendChild((0,t.createElement)(document,"div",{class:"ck ck-fullscreen__left-sidebar-sticky","data-ck-fullscreen":"left-sidebar-sticky"})),document.querySelector('[data-ck-fullscreen="left-sidebar-sticky"]').appendChild(i);const n=this._editor.plugins.get("PresenceListUI");this.moveToFullscreen(n.view.element,"presence-list")}_generateDocumentOutlineContainer(){const e=this._editor.t,i=(0,t.createElement)(document,"div",{class:"ck-fullscreen__left-sidebar-item ck-fullscreen__left-sidebar-item--no-margin"});i.innerHTML='\n\t\t\t<div class="ck ck-fullscreen__left-sidebar-header ck-fullscreen__document-outline-header"></div>\n\t\t',i.firstElementChild.innerText=e("Document outline");const n=(0,t.createElement)(document,"div",{class:"ck ck-fullscreen__left-sidebar-item ck-fullscreen__document-outline-wrapper"});n.innerHTML='\n\t\t\t<div class="ck ck-fullscreen__document-outline" data-ck-fullscreen="document-outline"></div>\n\t\t',document.querySelector('[data-ck-fullscreen="left-sidebar-sticky"]')||document.querySelector('[data-ck-fullscreen="left-sidebar"]').appendChild((0,t.createElement)(document,"div",{class:"ck ck-fullscreen__left-sidebar-sticky","data-ck-fullscreen":"left-sidebar-sticky"})),document.querySelector('[data-ck-fullscreen="left-sidebar"]').appendChild(n),document.querySelector('[data-ck-fullscreen="left-sidebar-sticky"]').appendChild(i);const r=this._editor.plugins.get("DocumentOutlineUI");r.view.documentOutlineContainer=document.querySelector('[data-ck-fullscreen="left-sidebar"]'),this.moveToFullscreen(r.view.element,"document-outline")}_restoreDocumentOutlineDefaultContainer(){const e=this._editor.plugins.get("DocumentOutlineUI");e.view.documentOutlineContainer=e.view.element}_overrideAnnotationsUIs(){const e=this._editor.plugins.get("AnnotationsUIs");this._annotationsUIsData=new Map(e.uisData);const t=new Map;for(const[i,n]of[...this._annotationsUIsData])n.filter!==e.defaultFilter&&t.set(i,n.filter);e.deactivateAll();const i=this._editor.plugins.get("Sidebar");function n(){t.has("wideSidebar")?e.activate("wideSidebar",t.get("wideSidebar")):t.size?e.activate("wideSidebar",(e=>[...t.values()].some((t=>t(e))))):e.switchTo("wideSidebar")}i.container?(n(),this.moveToFullscreen(i.container.firstElementChild,"right-sidebar"),i.setContainer(this.getWrapper().querySelector('[data-ck-fullscreen="right-sidebar"]'))):(i.setContainer(this.getWrapper().querySelector('[data-ck-fullscreen="right-sidebar"]')),n(),this.moveToFullscreen(i.container.firstElementChild,"right-sidebar"))}_restoreAnnotationsUIs(){const e=this._editor.plugins.get("Sidebar"),t=e.context.config.get("sidebar.container");t&&e.setContainer(t);const i=this._editor.plugins.get("AnnotationsUIs");i.deactivateAll();for(const[e,t]of[...this._annotationsUIsData])i.activate(e,t.filter);this._annotationsUIsData=null}_overrideRevisionHistoryCallbacks(){this._editor.config.set("revisionHistory.showRevisionViewerCallback",(async()=>{const e=await this._showRevisionViewerCallback();return this._editor.plugins.has("DocumentOutlineUI")&&(this.getWrapper().querySelector(".ck-fullscreen__document-outline-header").style.display="none"),this.restoreMovedElementLocation("editable"),this.restoreMovedElementLocation("toolbar"),this.restoreMovedElementLocation("right-sidebar"),this._annotationsUIsData&&this._restoreAnnotationsUIs(),this._editor.ui.view.menuBarView&&this._editor.ui.view.menuBarView.disable(),this.moveToFullscreen(e.ui.getEditableElement(),"editable"),this.moveToFullscreen(e.ui.view.toolbar.element,"toolbar"),this.moveToFullscreen(this._editor.config.get("revisionHistory.viewerSidebarContainer"),"right-sidebar"),e})),this._editor.config.set("revisionHistory.closeRevisionViewerCallback",(async()=>{this.restoreMovedElementLocation("toolbar"),this.restoreMovedElementLocation("editable"),this.restoreMovedElementLocation("right-sidebar"),this._editor.plugins.has("DocumentOutlineUI")&&(this.getWrapper().querySelector(".ck-fullscreen__document-outline-header").style.display=""),this.moveToFullscreen(this._editor.ui.getEditableElement(),"editable"),this.moveToFullscreen(this._editor.ui.view.toolbar.element,"toolbar"),await this._closeRevisionViewerCallback(),this._editor.plugins.has("AnnotationsUIs")&&this._overrideAnnotationsUIs(),this._editor.ui.view.menuBarView&&this._editor.ui.view.menuBarView.enable()}))}_restoreRevisionHistoryCallbacks(){this._editor.config.set("revisionHistory.showRevisionViewerCallback",(async()=>this._showRevisionViewerCallback())),this._editor.config.set("revisionHistory.closeRevisionViewerCallback",(async()=>this._closeRevisionViewerCallback()))}_registerFullscreenDialogPositionAdjustments(){const e=this._editor.plugins.get("Dialog");this._setNewDialogPosition(),e.on("change:isOpen",this.updateDialogPositionCallback,{priority:"highest"})}_unregisterFullscreenDialogPositionAdjustments(){const e=this._editor.plugins.get("Dialog"),t=e.view;t&&null===t.position&&(t.position=r.DialogViewPosition.EDITOR_TOP_SIDE),t&&t.updatePosition(),e.off("change:isOpen",this.updateDialogPositionCallback)}updateDialogPositionCallback=this._updateDialogPosition.bind(this);_updateDialogPosition(e,t,i){i&&this._setNewDialogPosition()}_setNewDialogPosition(){const e=this._editor.plugins.get("Dialog").view;if(!e||e.position!==r.DialogViewPosition.EDITOR_TOP_SIDE)return;const i=new t.Rect(this._wrapper).getVisible(),n=new t.Rect(document.querySelector(".ck-fullscreen__editable")).getVisible(),s=new t.Rect(e.element.querySelector(".ck-dialog")).getVisible(),o=new t.Rect(document.querySelector(".ck-fullscreen__editable-wrapper")).excludeScrollbarsAndBorders().getVisible().width-new t.Rect(document.querySelector(".ck-fullscreen__editable-wrapper")).getVisible().width;i&&n&&s&&(e.position=null,e.moveTo(i.left+i.width-s.width-28+o,n.top))}_saveAncestorsScrollPositions(e){let i=e.parentElement;if(i)for(;i;){const e=i.style.overflowY||t.global.window.getComputedStyle(i).overflowY,n=i.style.overflowX||t.global.window.getComputedStyle(i).overflowX;("auto"===e||"scroll"===e||"auto"===n||"scroll"===n||"HTML"===i.tagName)&&this._savedAncestorsScrollPositions.set(i,{scrollLeft:i.scrollLeft,scrollTop:i.scrollTop}),i=i.parentElement}}}class o extends s{_editor;constructor(e){super(e),this._editor=e}defaultOnEnter(){const e=this._editor.ui,t=e.view;return this._editor.plugins.has("Pagination")&&this._editor.plugins.get("Pagination").isEnabled&&this.moveToFullscreen(e.getEditableElement().parentElement.querySelector(".ck-pagination-view"),"pagination-view"),this.moveToFullscreen(e.getEditableElement(),"editable"),this.moveToFullscreen(t.toolbar.element,"toolbar"),t.toolbar.switchBehavior(!0===this._editor.config.get("fullscreen.toolbar.shouldNotGroupWhenFull")?"static":"dynamic"),this.getWrapper().setAttribute("dir",t.element.getAttribute("dir")),this.getWrapper().classList.add("ck-rounded-corners"),this._editor.config.get("fullscreen.menuBar.isVisible")&&(t.menuBarView||(t.menuBarView=new r.MenuBarView(this._editor.locale),t.menuBarView.render(),e.initMenuBar(t.menuBarView)),this.moveToFullscreen(t.menuBarView.element,"menu-bar")),this.getWrapper()}}class l extends s{_editor;constructor(e){super(e),this._editor=e}defaultOnEnter(){return this._editor.plugins.has("Pagination")&&this._editor.plugins.get("Pagination").isEnabled&&this.moveToFullscreen(this._editor.ui.getEditableElement().parentElement.querySelector(".ck-pagination-view"),"pagination-view"),this.moveToFullscreen(this._editor.ui.getEditableElement(),"editable"),this.moveToFullscreen(this._editor.ui.view.toolbar.element,"toolbar"),this._editor.ui.view.toolbar.switchBehavior(!0===this._editor.config.get("fullscreen.toolbar.shouldNotGroupWhenFull")?"static":"dynamic"),this._editor.config.get("fullscreen.menuBar.isVisible")&&this.moveToFullscreen(this._editor.ui.view.menuBarView.element,"menu-bar"),this.getWrapper()}}class c extends e.Command{fullscreenHandler;constructor(e){super(e),this.affectsData=!1,this.isEnabled=!0,this.value=!1,!function(e){return"ClassicEditor"===e.constructor.editorName}(e)?!function(e){return"DecoupledEditor"===e.constructor.editorName}(e)?this.fullscreenHandler=new s(e):this.fullscreenHandler=new l(e):this.fullscreenHandler=new o(e)}execute(){this.value?this._disableFullscreenMode():this._enableFullscreenMode()}_enableFullscreenMode(){this.fullscreenHandler.enable(),this.value=!0}_disableFullscreenMode(){this.fullscreenHandler.disable(),this.value=!1}}class a extends e.Plugin{static get pluginName(){return"FullscreenEditing"}static get isOfficialPlugin(){return!0}constructor(e){super(e),e.config.define("fullscreen.menuBar.isVisible",!0),e.config.define("fullscreen.toolbar.shouldNotGroupWhenFull",!0===e.config.get("toolbar.shouldNotGroupWhenFull"))}init(){this.editor.commands.add("toggleFullscreen",new c(this.editor));const e=this.editor.locale.t;this.editor.keystrokes.set("Ctrl+Shift+F",((e,i)=>{this.editor.execute("toggleFullscreen"),t.env.isBlink||(this.editor.editing.view.document.isFocused=!1,this.editor.ui.view.toolbar.focusTracker.focusedElement=null),this.editor.editing.view.scrollToTheSelection(),this.editor.editing.view.focus(),i()})),this.editor.accessibility.addKeystrokeInfos({keystrokes:[{label:e("Toggle fullscreen mode"),keystroke:"CTRL+SHIFT+F"}],categoryId:"navigation"})}}var d=i(355),u=i(591),h=i.n(u),p=i(639),f=i.n(p),_=i(128),g=i.n(_),m=i(21),b=i.n(m),v=i(51),k=i.n(v),w=i(998),y={attributes:{"data-cke":!0}};y.setAttributes=b(),y.insert=g().bind(null,"head"),y.domAPI=f(),y.insertStyleElement=k();h()(w.A,y);w.A&&w.A.locals&&w.A.locals;const E="toggleFullscreen";class C extends e.Plugin{static get requires(){return[a]}static get pluginName(){return"FullscreenUI"}static get isOfficialPlugin(){return!0}init(){const e=this.editor;e.ui.componentFactory.add("fullscreen",(()=>this._createButton(r.ButtonView))),e.ui.componentFactory.add("menuBar:fullscreen",(()=>this._createButton(r.MenuBarMenuListItemButtonView)))}_createButton(e){const i=this.editor,n=i.t,s=i.commands.get(E),o=new e(i.locale);return o.set({isToggleable:!0}),o.bind("isEnabled").to(s,"isEnabled"),o.bind("isOn").to(s,"value"),o instanceof r.MenuBarMenuListItemButtonView?o.set({role:"menuitemcheckbox",label:n("Fullscreen mode")}):(o.bind("icon").to(s,"value",(e=>e?d.IconFullscreenLeave:d.IconFullscreenEnter)),o.bind("label").to(s,"value",(e=>n(e?"Leave fullscreen mode":"Enter fullscreen mode"))),o.set({tooltip:!0})),this.listenTo(o,"execute",(()=>{i.execute(E),t.env.isBlink||(this.editor.ui.view.toolbar.focusTracker.focusedElement=null),i.editing.view.scrollToTheSelection(),i.editing.view.focus()})),o}}class x extends e.Plugin{static get requires(){return[a,C]}static get pluginName(){return"Fullscreen"}static get isOfficialPlugin(){return!0}}})(),(window.CKEditor5=window.CKEditor5||{}).fullscreen=n})();