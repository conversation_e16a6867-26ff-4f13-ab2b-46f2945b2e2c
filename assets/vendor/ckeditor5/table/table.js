!function(e){const t=e.en=e.en||{};t.dictionary=Object.assign(t.dictionary||{},{"Align cell text to the bottom":"Align cell text to the bottom","Align cell text to the center":"Align cell text to the center","Align cell text to the left":"Align cell text to the left","Align cell text to the middle":"Align cell text to the middle","Align cell text to the right":"Align cell text to the right","Align cell text to the top":"Align cell text to the top","Align table to the left":"Align table to the left","Align table to the right":"Align table to the right",Alignment:"Alignment",Background:"Background",Border:"Border","Cell properties":"Cell properties","Center table":"Center table","Choose table type":"Choose table type",Color:"Color",Column:"Column","Content table":"Content table",Dashed:"Dashed","Delete column":"Delete column","Delete row":"Delete row",Dimensions:"Dimensions",Dotted:"Dotted",Double:"Double","Enter table caption":"Enter table caption",Groove:"Groove","Header column":"Header column","Header row":"Header row",Height:"Height","Horizontal text alignment toolbar":"Horizontal text alignment toolbar","Insert a new table row (when in the last cell of a table)":"Insert a new table row (when in the last cell of a table)","Insert column left":"Insert column left","Insert column right":"Insert column right","Insert row above":"Insert row above","Insert row below":"Insert row below","Insert table":"Insert table","Insert table layout":"Insert table layout",Inset:"Inset","Justify cell text":"Justify cell text","Keystrokes that can be used in a table cell":"Keystrokes that can be used in a table cell","Layout table":"Layout table","Merge cell down":"Merge cell down","Merge cell left":"Merge cell left","Merge cell right":"Merge cell right","Merge cell up":"Merge cell up","Merge cells":"Merge cells","Move the selection to the next cell":"Move the selection to the next cell","Move the selection to the previous cell":"Move the selection to the previous cell","Navigate through the table":"Navigate through the table",None:"None",Outset:"Outset",Padding:"Padding",Ridge:"Ridge",Row:"Row","Select column":"Select column","Select row":"Select row",Solid:"Solid","Split cell horizontally":"Split cell horizontally","Split cell vertically":"Split cell vertically",Style:"Style",Table:"Table","Table alignment toolbar":"Table alignment toolbar","Table cell text alignment":"Table cell text alignment","Table layout":"Table layout","Table properties":"Table properties","Table toolbar":"Table toolbar","Table type":"Table type","Table type options":"Table type options",'The color is invalid. Try "#FF0000" or "rgb(255,0,0)" or "red".':'The color is invalid. Try "#FF0000" or "rgb(255,0,0)" or "red".','The value is invalid. Try "10px" or "2em" or simply "2".':'The value is invalid. Try "10px" or "2em" or simply "2".',"Vertical text alignment toolbar":"Vertical text alignment toolbar",Width:"Width"})}(window.CKEDITOR_TRANSLATIONS||(window.CKEDITOR_TRANSLATIONS={})),
/*!
 * @license Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.
 * For licensing, see LICENSE.md.
 */(()=>{var e={21:e=>{"use strict";e.exports=function(e,t){Object.keys(t).forEach((function(o){e.setAttribute(o,t[o])}))}},25:(e,t,o)=>{"use strict";o.d(t,{A:()=>a});var n=o(758),i=o.n(n),l=o(935),r=o.n(l)()(i());r.push([e.id,".ck-content[dir=rtl] .table th{text-align:right}.ck-content[dir=ltr] .table th{text-align:left}.ck-content figure.table:not(.layout-table){display:table}.ck-content figure.table:not(.layout-table)>table{height:100%;width:100%}.ck-content .table:not(.layout-table){margin:.9em auto}.ck-content figure.table:not(.layout-table)>table,.ck-content table.table:not(.layout-table){border:1px double #b3b3b3;border-collapse:collapse;border-spacing:0}.ck-content figure.table:not(.layout-table)>table>tbody>tr>th,.ck-content figure.table:not(.layout-table)>table>thead>tr>th,.ck-content table.table:not(.layout-table)>tbody>tr>th,.ck-content table.table:not(.layout-table)>thead>tr>th{background:rgba(0,0,0,.05);font-weight:700}.ck-content figure.table:not(.layout-table)>table>tbody>tr>td,.ck-content figure.table:not(.layout-table)>table>tbody>tr>th,.ck-content figure.table:not(.layout-table)>table>thead>tr>td,.ck-content figure.table:not(.layout-table)>table>thead>tr>th,.ck-content table.table:not(.layout-table)>tbody>tr>td,.ck-content table.table:not(.layout-table)>tbody>tr>th,.ck-content table.table:not(.layout-table)>thead>tr>td,.ck-content table.table:not(.layout-table)>thead>tr>th{border:1px solid #bfbfbf;min-width:2em;padding:.4em}@media print{.ck-content figure.table>table{height:auto}}.ck-editor__editable .ck-table-bogus-paragraph{display:inline-block;width:100%}",""]);const a=r},51:e=>{"use strict";e.exports=function(e){var t=document.createElement("style");return e.setAttributes(t,e.attributes),e.insert(t,e.options),t}},103:(e,t,o)=>{"use strict";o.d(t,{A:()=>a});var n=o(758),i=o.n(n),l=o(935),r=o.n(l)()(i());r.push([e.id,'.ck-editor__editable.ck-editor__editable_inline>.ck-widget.ck-widget_with-selection-handle.layout-table:first-child{margin-top:var(--ck-spacing-large)}.ck-editor__editable.ck-editor__editable_inline>.ck-widget.ck-widget_with-selection-handle.layout-table:last-child,.ck-editor__editable.ck-editor__editable_inline>.ck-widget.ck-widget_with-selection-handle.layout-table:nth-last-child(2):has(+.ck-fake-selection-container){margin-bottom:var(--ck-spacing-large)}.ck-editor__editable .table.layout-table{display:table;margin:0}.ck-editor__editable .table.layout-table.ck-widget:hover{z-index:var(--ck-z-default)}.ck-editor__editable .table.layout-table.ck-widget:hover>.ck-widget__selection-handle{opacity:.75;visibility:visible}.ck-editor__editable .table.layout-table.ck-widget:hover>.ck-widget__selection-handle:hover{opacity:1}.ck-editor__editable .table.layout-table.ck-widget:has(.ck-widget.table:hover)>.ck-widget__selection-handle{opacity:0;visibility:hidden}.ck-editor__editable .table.layout-table.ck-widget.ck-widget_selected{z-index:var(--ck-z-default)}.ck-content figure.table.layout-table,.ck-content table.table.layout-table{margin-bottom:0;margin-top:0}.ck-content figure.table.layout-table>table,.ck-content table.table.layout-table{border-spacing:0}:root{--ck-table-layout-widget-type-around-button-size:16px;--ck-table-layout-widget-type-around-icon-width:10px;--ck-table-layout-widget-type-around-icon-height:8px;--ck-table-layout-widget-handler-icon-size:10px;--ck-table-layout-default-border-color:#d4d4d4}.ck-editor__editable .table.layout-table>table{border-collapse:revert;height:100%;width:100%}.ck-editor__editable .table.layout-table>table:not([style*="border:"],[style*=border-top],[style*=border-bottom],[style*=border-left],[style*=border-right],[style*=border-width],[style*=border-style],[style*=border-color]){border-color:transparent;border-width:0;outline:none}.ck-editor__editable .table.layout-table>table>tbody>tr>td{box-shadow:revert;min-width:2em;padding:revert;text-indent:1px}.ck-editor__editable .table.layout-table>table>tbody>tr>td[style*=" width:"],.ck-editor__editable .table.layout-table>table>tbody>tr>td[style*="\'width:"],.ck-editor__editable .table.layout-table>table>tbody>tr>td[style*=";width:"],.ck-editor__editable .table.layout-table>table>tbody>tr>td[style^="width:"]{min-width:auto}.ck-editor__editable .table.layout-table>table>tbody>tr>td:focus{background-color:transparent}.ck-editor__editable .table.layout-table>table>tbody>tr>td:not([style*="border:"],[style*=border-top],[style*=border-bottom],[style*=border-left],[style*=border-right],[style*=border-width],[style*=border-style],[style*=border-color]){border-color:transparent;border-width:0;outline:var(--ck-table-layout-default-border-color) 1px dashed;outline-offset:-1px}.ck-editor__editable .table.layout-table>table>tbody>tr>td:not([style*="border:"],[style*=border-top],[style*=border-bottom],[style*=border-left],[style*=border-right],[style*=border-width],[style*=border-style],[style*=border-color]):focus{outline:var(--ck-color-focus-border) 1px solid}.ck-editor__editable .table.layout-table>table>tbody>tr>td>.ck-table-bogus-paragraph{text-indent:0;width:calc(100% - 1px)}.ck-editor__editable .table.layout-table.ck-widget>.ck-widget__type-around{--ck-widget-type-around-button-size:var(--ck-table-layout-widget-type-around-button-size)}.ck-editor__editable .table.layout-table.ck-widget>.ck-widget__type-around>.ck-widget__type-around__button.ck-widget__type-around__button_after,.ck-editor__editable .table.layout-table.ck-widget>.ck-widget__type-around>.ck-widget__type-around__button.ck-widget__type-around__button_before{transform:translateY(0);z-index:2}.ck-editor__editable .table.layout-table.ck-widget>.ck-widget__type-around>.ck-widget__type-around__button.ck-widget__type-around__button_before{border-radius:0 0 100px 100px;left:min(10%,30px);margin-left:var(--ck-table-layout-widget-type-around-button-size)}.ck-editor__editable .table.layout-table.ck-widget>.ck-widget__type-around>.ck-widget__type-around__button.ck-widget__type-around__button_before:after{border-radius:0 0 100px 100px}.ck-editor__editable .table.layout-table.ck-widget>.ck-widget__type-around>.ck-widget__type-around__button.ck-widget__type-around__button_after,.ck-editor__editable .table.layout-table.ck-widget>.ck-widget__type-around>.ck-widget__type-around__button.ck-widget__type-around__button_after:after{border-radius:100px 100px 0 0}.ck-editor__editable .table.layout-table.ck-widget>.ck-widget__type-around>.ck-widget__type-around__button svg{height:var(--ck-table-layout-widget-type-around-icon-height);width:var(--ck-table-layout-widget-type-around-icon-width)}.ck-editor__editable .table.layout-table.ck-widget.ck-widget_with-selection-handle>.ck-widget__selection-handle{--ck-widget-handler-icon-size:var(--ck-table-layout-widget-handler-icon-size);transform:translateY(calc(0px - var(--ck-widget-outline-thickness)));z-index:3}.ck-editor__editable .table.layout-table.ck-widget.ck-widget_type-around_show-fake-caret_before>.ck-widget__type-around>.ck-widget__type-around__fake-caret{top:0}.ck-editor__editable .table.layout-table.ck-widget.ck-widget_type-around_show-fake-caret_after>.ck-widget__type-around>.ck-widget__type-around__fake-caret{bottom:0}',""]);const a=r},128:e=>{"use strict";var t={};e.exports=function(e,o){var n=function(e){if(void 0===t[e]){var o=document.querySelector(e);if(window.HTMLIFrameElement&&o instanceof window.HTMLIFrameElement)try{o=o.contentDocument.head}catch(e){o=null}t[e]=o}return t[e]}(e);if(!n)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");n.appendChild(o)}},175:(e,t,o)=>{"use strict";o.d(t,{A:()=>a});var n=o(758),i=o.n(n),l=o(935),r=o.n(l)()(i());r.push([e.id,":root{--ck-color-selector-caption-background:#f7f7f7;--ck-color-selector-caption-text:#333;--ck-color-selector-caption-highlighted-background:#fd0}.ck-content .table>figcaption{background-color:var(--ck-color-selector-caption-background);caption-side:top;color:var(--ck-color-selector-caption-text);display:table-caption;font-size:.75em;outline-offset:-1px;padding:.6em;text-align:center;word-break:break-word}@media (forced-colors:active){.ck-content .table>figcaption{background-color:unset;color:unset}}@media (forced-colors:none){.ck.ck-editor__editable .table>figcaption.table__caption_highlighted{animation:ck-table-caption-highlight .6s ease-out}}.ck.ck-editor__editable .table>figcaption.ck-placeholder:before{overflow:hidden;padding-left:inherit;padding-right:inherit;text-overflow:ellipsis;white-space:nowrap}@keyframes ck-table-caption-highlight{0%{background-color:var(--ck-color-selector-caption-highlighted-background)}to{background-color:var(--ck-color-selector-caption-background)}}",""]);const a=r},218:(e,t,o)=>{"use strict";o.d(t,{A:()=>a});var n=o(758),i=o.n(n),l=o(935),r=o.n(l)()(i());r.push([e.id,".ck.ck-table-properties-form .ck-form__row.ck-table-properties-form__alignment-row{align-content:baseline;flex-basis:0;flex-wrap:wrap}.ck.ck-table-properties-form .ck-form__row.ck-table-properties-form__alignment-row .ck.ck-toolbar .ck-toolbar__items{flex-wrap:nowrap}.ck.ck-table-properties-form{width:320px}.ck.ck-table-properties-form .ck-form__row.ck-table-properties-form__alignment-row{align-self:flex-end;padding:0}.ck.ck-table-properties-form .ck-form__row.ck-table-properties-form__alignment-row .ck.ck-toolbar{background:none;margin-top:var(--ck-spacing-standard)}.ck.ck-table-properties-form .ck-form__row.ck-table-properties-form__alignment-row .ck.ck-toolbar .ck-toolbar__items>*{width:40px}",""]);const a=r},237:e=>{"use strict";e.exports=CKEditor5.dll},266:(e,t,o)=>{"use strict";o.d(t,{A:()=>a});var n=o(758),i=o.n(n),l=o(935),r=o.n(l)()(i());r.push([e.id,".ck.ck-table-cell-properties-form .ck-form__row.ck-table-cell-properties-form__alignment-row{flex-wrap:wrap}.ck.ck-table-cell-properties-form .ck-form__row.ck-table-cell-properties-form__alignment-row .ck.ck-toolbar:first-of-type{flex-grow:0.57}.ck.ck-table-cell-properties-form .ck-form__row.ck-table-cell-properties-form__alignment-row .ck.ck-toolbar:last-of-type{flex-grow:0.43}.ck.ck-table-cell-properties-form .ck-form__row.ck-table-cell-properties-form__alignment-row .ck.ck-toolbar .ck-button{flex-grow:1}.ck.ck-table-cell-properties-form{width:320px}.ck.ck-table-cell-properties-form .ck-form__row.ck-table-cell-properties-form__padding-row{align-self:flex-end;padding:0;width:25%}.ck.ck-table-cell-properties-form .ck-form__row.ck-table-cell-properties-form__alignment-row .ck.ck-toolbar{background:none;margin-top:var(--ck-spacing-standard)}",""]);const a=r},311:(e,t,o)=>{e.exports=o(237)("./src/ui.js")},331:(e,t,o)=>{e.exports=o(237)("./src/clipboard.js")},355:(e,t,o)=>{e.exports=o(237)("./src/icons.js")},363:(e,t,o)=>{"use strict";o.d(t,{A:()=>a});var n=o(758),i=o.n(n),l=o(935),r=o.n(l)()(i());r.push([e.id,":root{--ck-color-selector-column-resizer-hover:var(--ck-color-base-active);--ck-table-column-resizer-width:7px;--ck-table-column-resizer-position-offset:calc(var(--ck-table-column-resizer-width)*-0.5 - 0.5px)}.ck-content .table .ck-table-resized{table-layout:fixed}.ck-content .table td,.ck-content .table th{overflow-wrap:break-word}.ck.ck-editor__editable .table td,.ck.ck-editor__editable .table th{position:relative}.ck.ck-editor__editable .table .ck-table-column-resizer{bottom:0;cursor:col-resize;position:absolute;right:var(--ck-table-column-resizer-position-offset);top:0;user-select:none;width:var(--ck-table-column-resizer-width);z-index:var(--ck-z-default)}.ck.ck-editor__editable .table[draggable] .ck-table-column-resizer,.ck.ck-editor__editable.ck-column-resize_disabled .table .ck-table-column-resizer{display:none}.ck.ck-editor__editable .table .ck-table-column-resizer:hover,.ck.ck-editor__editable .table .ck-table-column-resizer__active{background-color:var(--ck-color-selector-column-resizer-hover);opacity:.25}.ck.ck-editor__editable[dir=rtl] .table .ck-table-column-resizer{left:var(--ck-table-column-resizer-position-offset);right:unset}",""]);const a=r},584:(e,t,o)=>{e.exports=o(237)("./src/utils.js")},591:e=>{"use strict";var t=[];function o(e){for(var o=-1,n=0;n<t.length;n++)if(t[n].identifier===e){o=n;break}return o}function n(e,n){for(var l={},r=[],a=0;a<e.length;a++){var s=e[a],c=n.base?s[0]+n.base:s[0],d=l[c]||0,u="".concat(c," ").concat(d);l[c]=d+1;var b=o(u),h={css:s[1],media:s[2],sourceMap:s[3],supports:s[4],layer:s[5]};if(-1!==b)t[b].references++,t[b].updater(h);else{var m=i(h,n);n.byIndex=a,t.splice(a,0,{identifier:u,updater:m,references:1})}r.push(u)}return r}function i(e,t){var o=t.domAPI(t);o.update(e);return function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap&&t.supports===e.supports&&t.layer===e.layer)return;o.update(e=t)}else o.remove()}}e.exports=function(e,i){var l=n(e=e||[],i=i||{});return function(e){e=e||[];for(var r=0;r<l.length;r++){var a=o(l[r]);t[a].references--}for(var s=n(e,i),c=0;c<l.length;c++){var d=o(l[c]);0===t[d].references&&(t[d].updater(),t.splice(d,1))}l=s}}},639:e=>{"use strict";var t,o=(t=[],function(e,o){return t[e]=o,t.filter(Boolean).join("\n")});function n(e,t,n,i){var l;if(n)l="";else{l="",i.supports&&(l+="@supports (".concat(i.supports,") {")),i.media&&(l+="@media ".concat(i.media," {"));var r=void 0!==i.layer;r&&(l+="@layer".concat(i.layer.length>0?" ".concat(i.layer):""," {")),l+=i.css,r&&(l+="}"),i.media&&(l+="}"),i.supports&&(l+="}")}if(e.styleSheet)e.styleSheet.cssText=o(t,l);else{var a=document.createTextNode(l),s=e.childNodes;s[t]&&e.removeChild(s[t]),s.length?e.insertBefore(a,s[t]):e.appendChild(a)}}var i={singleton:null,singletonCounter:0};e.exports=function(e){if("undefined"==typeof document)return{update:function(){},remove:function(){}};var t=i.singletonCounter++,o=i.singleton||(i.singleton=e.insertStyleElement(e));return{update:function(e){n(o,t,!1,e)},remove:function(e){n(o,t,!0,e)}}}},712:(e,t,o)=>{"use strict";o.d(t,{A:()=>a});var n=o(758),i=o.n(n),l=o(935),r=o.n(l)()(i());r.push([e.id,".ck .ck-insert-table-dropdown__grid{display:flex;flex-direction:row;flex-wrap:wrap}:root{--ck-insert-table-dropdown-padding:10px;--ck-insert-table-dropdown-box-height:11px;--ck-insert-table-dropdown-box-width:12px;--ck-insert-table-dropdown-box-margin:1px}.ck .ck-insert-table-dropdown__grid{padding:var(--ck-insert-table-dropdown-padding) var(--ck-insert-table-dropdown-padding) 0;width:calc(var(--ck-insert-table-dropdown-box-width)*10 + var(--ck-insert-table-dropdown-box-margin)*20 + var(--ck-insert-table-dropdown-padding)*2)}.ck .ck-insert-table-dropdown__label,.ck[dir=rtl] .ck-insert-table-dropdown__label{text-align:center}.ck .ck-insert-table-dropdown-grid-box{border:1px solid var(--ck-color-base-border);border-radius:1px;margin:var(--ck-insert-table-dropdown-box-margin);min-height:var(--ck-insert-table-dropdown-box-height);min-width:var(--ck-insert-table-dropdown-box-width);outline:none;transition:none}@media (prefers-reduced-motion:reduce){.ck .ck-insert-table-dropdown-grid-box{transition:none}}.ck .ck-insert-table-dropdown-grid-box:focus{box-shadow:none}.ck .ck-insert-table-dropdown-grid-box.ck-on{background:var(--ck-color-focus-outer-shadow);border-color:var(--ck-color-focus-border)}",""]);const a=r},719:(e,t,o)=>{"use strict";o.d(t,{A:()=>a});var n=o(758),i=o.n(n),l=o(935),r=o.n(l)()(i());r.push([e.id,':root{--ck-table-selected-cell-background:rgba(158,207,250,.3)}.ck.ck-editor__editable .table table td.ck-editor__editable_selected,.ck.ck-editor__editable .table table th.ck-editor__editable_selected{box-shadow:unset;caret-color:transparent;position:relative}.ck.ck-editor__editable .table table td.ck-editor__editable_selected:after,.ck.ck-editor__editable .table table th.ck-editor__editable_selected:after{background-color:var(--ck-table-selected-cell-background);bottom:0;content:"";left:0;pointer-events:none;position:absolute;right:0;top:0}.ck.ck-editor__editable .table table td.ck-editor__editable_selected ::selection,.ck.ck-editor__editable .table table td.ck-editor__editable_selected:focus,.ck.ck-editor__editable .table table th.ck-editor__editable_selected ::selection,.ck.ck-editor__editable .table table th.ck-editor__editable_selected:focus{background-color:transparent}.ck.ck-editor__editable .table table td.ck-editor__editable_selected .ck-widget,.ck.ck-editor__editable .table table th.ck-editor__editable_selected .ck-widget{outline:unset}.ck.ck-editor__editable .table table td.ck-editor__editable_selected .ck-widget>.ck-widget__selection-handle,.ck.ck-editor__editable .table table th.ck-editor__editable_selected .ck-widget>.ck-widget__selection-handle{display:none}',""]);const a=r},758:e=>{"use strict";e.exports=function(e){return e[1]}},770:(e,t,o)=>{"use strict";o.d(t,{A:()=>a});var n=o(758),i=o.n(n),l=o(935),r=o.n(l)()(i());r.push([e.id,".ck.ck-input-color{display:flex;flex-direction:row-reverse;width:100%}.ck.ck-input-color>input.ck.ck-input-text{flex-grow:1;min-width:auto}.ck.ck-input-color>div.ck.ck-dropdown{min-width:auto}.ck.ck-input-color>div.ck.ck-dropdown>.ck-input-color__button .ck-dropdown__arrow{display:none}.ck.ck-input-color .ck.ck-input-color__button{display:flex}.ck.ck-input-color .ck.ck-input-color__button .ck.ck-input-color__button__preview{overflow:hidden;position:relative}.ck.ck-input-color .ck.ck-input-color__button .ck.ck-input-color__button__preview>.ck.ck-input-color__button__preview__no-color-indicator{display:block;position:absolute}[dir=ltr] .ck.ck-input-color>.ck.ck-input-text{border-bottom-right-radius:0;border-top-right-radius:0}[dir=rtl] .ck.ck-input-color>.ck.ck-input-text{border-bottom-left-radius:0;border-top-left-radius:0}.ck.ck-input-color>.ck.ck-input-text:focus{z-index:0}.ck.ck-input-color>.ck.ck-dropdown>.ck.ck-button.ck-input-color__button{padding:0}[dir=ltr] .ck.ck-input-color>.ck.ck-dropdown>.ck.ck-button.ck-input-color__button{border-bottom-left-radius:0;border-top-left-radius:0}[dir=ltr] .ck.ck-input-color>.ck.ck-dropdown>.ck.ck-button.ck-input-color__button:not(:focus){border-left:1px solid transparent}[dir=rtl] .ck.ck-input-color>.ck.ck-dropdown>.ck.ck-button.ck-input-color__button{border-bottom-right-radius:0;border-top-right-radius:0}[dir=rtl] .ck.ck-input-color>.ck.ck-dropdown>.ck.ck-button.ck-input-color__button:not(:focus){border-right:1px solid transparent}.ck.ck-input-color>.ck.ck-dropdown>.ck.ck-button.ck-input-color__button.ck-disabled{background:var(--ck-color-input-disabled-background)}.ck.ck-input-color>.ck.ck-dropdown>.ck.ck-button.ck-input-color__button>.ck.ck-input-color__button__preview{border:1px solid var(--ck-color-input-border);border-radius:0;height:20px;width:20px}.ck-rounded-corners .ck.ck-input-color>.ck.ck-dropdown>.ck.ck-button.ck-input-color__button>.ck.ck-input-color__button__preview,.ck.ck-input-color>.ck.ck-dropdown>.ck.ck-button.ck-input-color__button>.ck.ck-input-color__button__preview.ck-rounded-corners{border-radius:var(--ck-border-radius)}.ck.ck-input-color>.ck.ck-dropdown>.ck.ck-button.ck-input-color__button>.ck.ck-input-color__button__preview>.ck.ck-input-color__button__preview__no-color-indicator{background:red;border-radius:2px;height:150%;left:50%;top:-30%;transform:rotate(45deg);transform-origin:50%;width:8%}.ck.ck-input-color .ck.ck-input-color__remove-color{border-bottom-left-radius:0;border-bottom-right-radius:0;padding:calc(var(--ck-spacing-standard)/2) var(--ck-spacing-standard);width:100%}.ck.ck-input-color .ck.ck-input-color__remove-color:not(:focus){border-bottom:1px solid var(--ck-color-input-border)}[dir=ltr] .ck.ck-input-color .ck.ck-input-color__remove-color{border-top-right-radius:0}[dir=rtl] .ck.ck-input-color .ck.ck-input-color__remove-color{border-top-left-radius:0}.ck.ck-input-color .ck.ck-input-color__remove-color .ck.ck-icon{margin-right:var(--ck-spacing-standard)}[dir=rtl] .ck.ck-input-color .ck.ck-input-color__remove-color .ck.ck-icon{margin-left:var(--ck-spacing-standard);margin-right:0}",""]);const a=r},782:(e,t,o)=>{e.exports=o(237)("./src/core.js")},783:(e,t,o)=>{e.exports=o(237)("./src/engine.js")},817:(e,t,o)=>{"use strict";o.d(t,{A:()=>a});var n=o(758),i=o.n(n),l=o(935),r=o.n(l)()(i());r.push([e.id,':root{--ck-color-selector-focused-cell-background:rgba(158,201,250,.3);--ck-table-content-default-border-color:#d4d4d4}.ck-widget.table table[style*="border-style:none"],.ck-widget.table table[style*="border:none"]{outline:var(--ck-table-content-default-border-color) 1px dashed}.ck-widget.table td.ck-editor__nested-editable,.ck-widget.table th.ck-editor__nested-editable{outline:unset}.ck-widget.table td.ck-editor__nested-editable.ck-editor__nested-editable_focused:not(.ck-editor__editable_selected),.ck-widget.table td.ck-editor__nested-editable:not(.ck-editor__editable_selected):focus,.ck-widget.table th.ck-editor__nested-editable.ck-editor__nested-editable_focused:not(.ck-editor__editable_selected),.ck-widget.table th.ck-editor__nested-editable:not(.ck-editor__editable_selected):focus{background:var(--ck-color-selector-focused-cell-background);outline:1px solid var(--ck-color-focus-border);outline-offset:-1px}',""]);const a=r},839:(e,t,o)=>{"use strict";o.d(t,{A:()=>a});var n=o(758),i=o.n(n),l=o(935),r=o.n(l)()(i());r.push([e.id,".ck.ck-form__row.ck-table-form__action-row .ck-button-cancel,.ck.ck-form__row.ck-table-form__action-row .ck-button-save{justify-content:center}.ck.ck-form__row>:not(.ck-label)+*{margin-inline-start:var(--ck-spacing-large)}.ck.ck-form__row>.ck-label{min-width:100%;width:100%}.ck.ck-form__row.ck-table-form__action-row{margin-top:var(--ck-spacing-large)}.ck.ck-form__row.ck-table-form__action-row .ck-button .ck-button__label{color:var(--ck-color-text)}",""]);const a=r},901:(e,t,o)=>{e.exports=o(237)("./src/widget.js")},911:(e,t,o)=>{"use strict";o.d(t,{A:()=>a});var n=o(758),i=o.n(n),l=o(935),r=o.n(l)()(i());r.push([e.id,'.ck.ck-table-form .ck-form__row.ck-table-form__background-row,.ck.ck-table-form .ck-form__row.ck-table-form__border-row{flex-wrap:wrap}.ck.ck-table-form .ck-form__row.ck-table-form__dimensions-row{align-items:center;flex-wrap:wrap}.ck.ck-table-form .ck-form__row.ck-table-form__dimensions-row .ck-labeled-field-view{align-items:center;display:flex;flex-direction:column-reverse}.ck.ck-table-form .ck-form__row.ck-table-form__dimensions-row .ck-labeled-field-view .ck.ck-dropdown,.ck.ck-table-form .ck-form__row.ck-table-form__dimensions-row .ck-table-form__dimension-operator{flex-grow:0}.ck.ck-table-form .ck-form__row>:not(.ck-label,.ck-table-form__dimension-operator){flex-grow:1}.ck.ck-table-form .ck.ck-labeled-field-view{position:relative}.ck.ck-table-form .ck.ck-labeled-field-view .ck.ck-labeled-field-view__status{bottom:calc(var(--ck-table-properties-error-arrow-size)*-1);left:50%;position:absolute;transform:translate(-50%,100%);z-index:1}.ck.ck-table-form .ck.ck-labeled-field-view .ck.ck-labeled-field-view__status:after{content:"";left:50%;position:absolute;top:calc(var(--ck-table-properties-error-arrow-size)*-1);transform:translateX(-50%)}:root{--ck-table-properties-error-arrow-size:6px;--ck-table-properties-min-error-width:150px}.ck.ck-table-form .ck-form__row.ck-table-form__border-row .ck-labeled-field-view>.ck-label{font-size:var(--ck-font-size-tiny);text-align:center}.ck.ck-table-form .ck-form__row.ck-table-form__border-row .ck-table-form__border-style,.ck.ck-table-form .ck-form__row.ck-table-form__border-row .ck-table-form__border-width{max-width:80px;min-width:80px;width:80px}.ck.ck-table-form .ck-form__row.ck-table-form__dimensions-row{padding:0}.ck.ck-table-form .ck-form__row.ck-table-form__dimensions-row .ck-table-form__dimensions-row__height,.ck.ck-table-form .ck-form__row.ck-table-form__dimensions-row .ck-table-form__dimensions-row__width{margin:0}.ck.ck-table-form .ck-form__row.ck-table-form__dimensions-row .ck-table-form__dimension-operator{align-self:flex-end;display:inline-block;height:var(--ck-ui-component-min-height);line-height:var(--ck-ui-component-min-height);margin:0 var(--ck-spacing-small)}.ck.ck-table-form .ck.ck-labeled-field-view{padding-top:var(--ck-spacing-standard)}.ck.ck-table-form .ck.ck-labeled-field-view .ck.ck-labeled-field-view__status{animation:ck-table-form-labeled-view-status-appear .15s ease both;background:var(--ck-color-base-error);border-radius:0;color:var(--ck-color-base-background);min-width:var(--ck-table-properties-min-error-width);padding:var(--ck-spacing-small) var(--ck-spacing-medium);text-align:center}.ck-rounded-corners .ck.ck-table-form .ck.ck-labeled-field-view .ck.ck-labeled-field-view__status,.ck.ck-table-form .ck.ck-labeled-field-view .ck.ck-labeled-field-view__status.ck-rounded-corners{border-radius:var(--ck-border-radius)}.ck.ck-table-form .ck.ck-labeled-field-view .ck.ck-labeled-field-view__status:after{border-color:transparent transparent var(--ck-color-base-error) transparent;border-style:solid;border-width:0 var(--ck-table-properties-error-arrow-size) var(--ck-table-properties-error-arrow-size) var(--ck-table-properties-error-arrow-size)}@media (prefers-reduced-motion:reduce){.ck.ck-table-form .ck.ck-labeled-field-view .ck.ck-labeled-field-view__status{animation:none}}.ck.ck-table-form .ck.ck-labeled-field-view .ck-input.ck-error:not(:focus)+.ck.ck-labeled-field-view__status{display:none}@keyframes ck-table-form-labeled-view-status-appear{0%{opacity:0}to{opacity:1}}',""]);const a=r},922:(e,t,o)=>{"use strict";o.d(t,{A:()=>a});var n=o(758),i=o.n(n),l=o(935),r=o.n(l)()(i());r.push([e.id,":root{--ck-form-default-width:340px}.ck.ck-form{padding:0 0 var(--ck-spacing-large)}.ck.ck-form.ck-form_default-width{width:var(--ck-form-default-width)}.ck.ck-form:focus{outline:none}.ck.ck-form .ck.ck-input-number,.ck.ck-form .ck.ck-input-text{min-width:100%;width:0}.ck.ck-form .ck.ck-dropdown{min-width:100%}.ck.ck-form .ck.ck-dropdown .ck-dropdown__button:not(:focus){border:1px solid var(--ck-color-base-border)}.ck.ck-form .ck.ck-dropdown .ck-dropdown__button .ck-button__label{width:100%}@media screen and (max-width:600px){.ck.ck-form.ck-responsive-form .ck.ck-form__row.ck-form__row_with-submit{align-items:stretch;flex-direction:column;padding:0}.ck.ck-form.ck-responsive-form .ck.ck-form__row.ck-form__row_with-submit>.ck{margin:var(--ck-spacing-large) var(--ck-spacing-large) 0}.ck.ck-form.ck-responsive-form .ck.ck-form__row.ck-form__row_with-submit .ck-button_with-text{justify-content:center}.ck.ck-form.ck-responsive-form .ck.ck-form__row.ck-form__row_large-bottom-padding{padding-bottom:var(--ck-spacing-large)}}[dir=ltr] .ck.ck-form.ck-responsive-form>:not(:first-child),[dir=rtl] .ck.ck-form.ck-responsive-form>:not(:last-child){margin-left:0}",""]);const a=r},935:e=>{"use strict";e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var o="",n=void 0!==t[5];return t[4]&&(o+="@supports (".concat(t[4],") {")),t[2]&&(o+="@media ".concat(t[2]," {")),n&&(o+="@layer".concat(t[5].length>0?" ".concat(t[5]):""," {")),o+=e(t),n&&(o+="}"),t[2]&&(o+="}"),t[4]&&(o+="}"),o})).join("")},t.i=function(e,o,n,i,l){"string"==typeof e&&(e=[[null,e,void 0]]);var r={};if(n)for(var a=0;a<this.length;a++){var s=this[a][0];null!=s&&(r[s]=!0)}for(var c=0;c<e.length;c++){var d=[].concat(e[c]);n&&r[d[0]]||(void 0!==l&&(void 0===d[5]||(d[1]="@layer".concat(d[5].length>0?" ".concat(d[5]):""," {").concat(d[1],"}")),d[5]=l),o&&(d[2]?(d[1]="@media ".concat(d[2]," {").concat(d[1],"}"),d[2]=o):d[2]=o),i&&(d[4]?(d[1]="@supports (".concat(d[4],") {").concat(d[1],"}"),d[4]=i):d[4]="".concat(i)),t.push(d))}},t}}},t={};function o(n){var i=t[n];if(void 0!==i)return i.exports;var l=t[n]={id:n,exports:{}};return e[n](l,l.exports,o),l.exports}o.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return o.d(t,{a:t}),t},o.d=(e,t)=>{for(var n in t)o.o(t,n)&&!o.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},o.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),o.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};(()=>{"use strict";o.r(n),o.d(n,{PlainTableOutput:()=>at,Table:()=>rt,TableCaption:()=>_n,TableCaptionEditing:()=>gn,TableCaptionUI:()=>pn,TableCellProperties:()=>go,TableCellPropertiesEditing:()=>mo,TableCellPropertiesUI:()=>Zt,TableCellWidthEditing:()=>oo,TableClipboard:()=>Je,TableColumnResize:()=>Do,TableColumnResizeEditing:()=>Eo,TableEditing:()=>We,TableKeyboard:()=>Ze,TableLayout:()=>jo,TableLayoutEditing:()=>No,TableMouse:()=>ot,TableProperties:()=>cn,TablePropertiesEditing:()=>en,TablePropertiesUI:()=>sn,TableSelection:()=>qe,TableToolbar:()=>ht,TableUI:()=>je,TableUtils:()=>ne});var e=o(782),t=o(901),i=o(584);function l(e,t){const{modelAttribute:o,styleName:n,viewElement:i,defaultValue:l,reduceBoxSides:r=!1,shouldUpcast:a=()=>!0}=t;e.for("upcast").attributeToAttribute({view:{name:i,styles:{[n]:/[\s\S]+/}},model:{key:o,value:(e,t,o)=>{if(!a(e))return;const i=c(l,"",o),s=e.getNormalizedStyle(n),u=r?d(s):s;return i!==u?u:void 0}}})}function r(e,t,o,n){e.for("upcast").add((e=>e.on("element:"+t,((e,t,i)=>{if(!t.modelRange)return;const l=["border-top-width","border-top-color","border-top-style","border-bottom-width","border-bottom-color","border-bottom-style","border-right-width","border-right-color","border-right-style","border-left-width","border-left-color","border-left-style"].filter((e=>t.viewItem.hasStyle(e)));if(!l.length)return;const r={styles:l};if(!i.consumable.test(t.viewItem,r))return;const a=[...t.modelRange.getItems({shallow:!0})].pop(),s=a.findAncestor("table",{includeSelf:!0});let c=n;s&&"layout"==s.getAttribute("tableType")&&(c={style:"none",color:"",width:""}),i.consumable.consume(t.viewItem,r);const u={style:t.viewItem.getNormalizedStyle("border-style"),color:t.viewItem.getNormalizedStyle("border-color"),width:t.viewItem.getNormalizedStyle("border-width")},b={style:d(u.style),color:d(u.color),width:d(u.width)};b.style!==c.style&&i.writer.setAttribute(o.style,b.style,a),b.color!==c.color&&i.writer.setAttribute(o.color,b.color,a),b.width!==c.width&&i.writer.setAttribute(o.width,b.width,a)}))))}function a(e,t){const{modelElement:o,modelAttribute:n,styleName:i}=t;e.for("downcast").attributeToAttribute({model:{name:o,key:n},view:e=>({key:"style",value:{[i]:e}})})}function s(e,t){const{modelAttribute:o,styleName:n}=t;e.for("downcast").add((e=>e.on(`attribute:${o}:table`,((e,t,o)=>{const{item:i,attributeNewValue:l}=t,{mapper:r,writer:a}=o;if(!o.consumable.consume(t.item,e.name))return;const s=[...r.toViewElement(i).getChildren()].find((e=>e.is("element","table")));l?a.setStyle(n,l,s):a.removeStyle(n,s)}))))}function c(e,t,o){const n=o.modelRange&&(0,i.first)(o.modelRange.getItems({shallow:!0})),l=n&&n.is("element")&&n.findAncestor("table",{includeSelf:!0});return l&&"layout"===l.getAttribute("tableType")?t:e}function d(e){if(!e)return;const t=["top","right","bottom","left"];if(!t.every((t=>e[t])))return e;const o=e.top;return t.every((t=>e[t]===o))?o:e}function u(e,t,o,n,i=1){null!=t&&null!=i&&t>i?n.setAttribute(e,t,o):n.removeAttribute(e,o)}function b(e,t,o={}){const n=e.createElement("tableCell",o);return e.insertElement("paragraph",n),e.insert(n,t),n}function h(e,t){const o=t.parent.parent,n=parseInt(o.getAttribute("headingColumns")||"0"),{column:i}=e.getCellLocation(t);return!!n&&i<n}function m(e,t,o){const{modelAttribute:n}=o;e.extend("tableCell",{allowAttributes:[n]}),l(t,{viewElement:/^(td|th)$/,...o}),a(t,{modelElement:"tableCell",...o})}function g(e){const t=e.getSelectedElement();return t&&t.is("element","table")?t:e.getFirstPosition().findAncestor("table")}function p(){return e=>{e.on("element:table",((e,t,o)=>{const n=t.viewItem;if(!o.consumable.test(n,{name:!0}))return;const{rows:i,headingRows:l,headingColumns:r}=function(e){let t,o=0;const n=[],i=[];let l;for(const r of Array.from(e.getChildren())){if("tbody"!==r.name&&"thead"!==r.name&&"tfoot"!==r.name)continue;"thead"!==r.name||l||(l=r);const e=Array.from(r.getChildren()).filter((e=>e.is("element","tr")));let a=null;for(const s of e){const e=Array.from(s.getChildren()).filter((e=>e.is("element","td")||e.is("element","th")));if(l&&r===l||"tbody"===r.name&&e.length>0&&(null===a||e.length===a)&&e.every((e=>e.is("element","th"))))o++,n.push(s);else{i.push(s);const e=w(s);(!t||e<t)&&(t=e)}a=Math.max(a||0,e.length)}}return{headingRows:o,headingColumns:t||0,rows:[...n,...i]}}(n),a={};r&&(a.headingColumns=r),l&&(a.headingRows=l);const s=o.writer.createElement("table",a);if(o.safeInsert(s,t.modelCursor)){if(o.consumable.consume(n,{name:!0}),i.forEach((e=>o.convertItem(e,o.writer.createPositionAt(s,"end")))),o.convertChildren(n,o.writer.createPositionAt(s,"end")),s.isEmpty){const e=o.writer.createElement("tableRow");o.writer.insert(e,o.writer.createPositionAt(s,"end")),b(o.writer,o.writer.createPositionAt(e,"end"))}o.updateConversionResult(s,t)}}))}}function f(e){return t=>{t.on(`element:${e}`,((e,t,{writer:o})=>{if(!t.modelRange)return;const n=t.modelRange.start.nodeAfter,i=o.createPositionAt(n,0);if(t.viewItem.isEmpty)return void o.insertElement("paragraph",i);const l=Array.from(n.getChildren());if(l.every((e=>e.is("element","$marker")))){const e=o.createElement("paragraph");o.insert(e,o.createPositionAt(n,0));for(const t of l)o.move(o.createRangeOn(t),o.createPositionAt(e,"end"))}}),{priority:"low"})}}function w(e){let t=0,o=0;const n=Array.from(e.getChildren()).filter((e=>"th"===e.name||"td"===e.name));for(;o<n.length&&"th"===n[o].name;){const e=n[o];t+=parseInt(e.getAttribute("colspan")||"1"),o++}return t}class _{_table;_startRow;_endRow;_startColumn;_endColumn;_includeAllSlots;_skipRows;_row;_rowIndex;_column;_cellIndex;_spannedCells;_nextCellAtColumn;_jumpedToStartRow=!1;constructor(e,t={}){this._table=e,this._startRow=void 0!==t.row?t.row:t.startRow||0,this._endRow=void 0!==t.row?t.row:t.endRow,this._startColumn=void 0!==t.column?t.column:t.startColumn||0,this._endColumn=void 0!==t.column?t.column:t.endColumn,this._includeAllSlots=!!t.includeAllSlots,this._skipRows=new Set,this._row=0,this._rowIndex=0,this._column=0,this._cellIndex=0,this._spannedCells=new Map,this._nextCellAtColumn=-1}[Symbol.iterator](){return this}next(){this._canJumpToStartRow()&&this._jumpToNonSpannedRowClosestToStartRow();const e=this._table.getChild(this._rowIndex);if(!e||this._isOverEndRow())return{done:!0,value:void 0};if(!e.is("element","tableRow"))return this._rowIndex++,this.next();if(this._isOverEndColumn())return this._advanceToNextRow();let t=null;const o=this._getSpanned();if(o)this._includeAllSlots&&!this._shouldSkipSlot()&&(t=this._formatOutValue(o.cell,o.row,o.column));else{const o=e.getChild(this._cellIndex);if(!o)return this._advanceToNextRow();const n=parseInt(o.getAttribute("colspan")||"1"),i=parseInt(o.getAttribute("rowspan")||"1");(n>1||i>1)&&this._recordSpans(o,i,n),this._shouldSkipSlot()||(t=this._formatOutValue(o)),this._nextCellAtColumn=this._column+n}return this._column++,this._column==this._nextCellAtColumn&&this._cellIndex++,t||this.next()}skipRow(e){this._skipRows.add(e)}_advanceToNextRow(){return this._row++,this._rowIndex++,this._column=0,this._cellIndex=0,this._nextCellAtColumn=-1,this.next()}_isOverEndRow(){return void 0!==this._endRow&&this._row>this._endRow}_isOverEndColumn(){return void 0!==this._endColumn&&this._column>this._endColumn}_formatOutValue(e,t=this._row,o=this._column){return{done:!1,value:new k(this,e,t,o)}}_shouldSkipSlot(){const e=this._skipRows.has(this._row),t=this._row<this._startRow,o=this._column<this._startColumn,n=void 0!==this._endColumn&&this._column>this._endColumn;return e||t||o||n}_getSpanned(){const e=this._spannedCells.get(this._row);return e&&e.get(this._column)||null}_recordSpans(e,t,o){const n={cell:e,row:this._row,column:this._column};for(let e=this._row;e<this._row+t;e++)for(let t=this._column;t<this._column+o;t++)e==this._row&&t==this._column||this._markSpannedCell(e,t,n)}_markSpannedCell(e,t,o){this._spannedCells.has(e)||this._spannedCells.set(e,new Map);this._spannedCells.get(e).set(t,o)}_canJumpToStartRow(){return!!this._startRow&&this._startRow>0&&!this._jumpedToStartRow}_jumpToNonSpannedRowClosestToStartRow(){const e=this._getRowLength(0);for(let t=this._startRow;!this._jumpedToStartRow;t--)e===this._getRowLength(t)&&(this._row=t,this._rowIndex=t,this._jumpedToStartRow=!0)}_getRowLength(e){return[...this._table.getChild(e).getChildren()].reduce(((e,t)=>e+parseInt(t.getAttribute("colspan")||"1")),0)}}class k{cell;row;column;cellAnchorRow;cellAnchorColumn;_cellIndex;_rowIndex;_table;constructor(e,t,o,n){this.cell=t,this.row=e._row,this.column=e._column,this.cellAnchorRow=o,this.cellAnchorColumn=n,this._cellIndex=e._cellIndex,this._rowIndex=e._rowIndex,this._table=e._table}get isAnchor(){return this.row===this.cellAnchorRow&&this.column===this.cellAnchorColumn}get cellWidth(){return parseInt(this.cell.getAttribute("colspan")||"1")}get cellHeight(){return parseInt(this.cell.getAttribute("rowspan")||"1")}get rowIndex(){return this._rowIndex}getPositionBefore(){return this._table.root.document.model.createPositionAt(this._table.getChild(this.row),this._cellIndex)}}function C(e,o){return(n,{writer:i})=>{const l=n.getAttribute("headingRows")||0,r=i.createContainerElement("table",null,[]),a=i.createContainerElement("figure",{class:"table"},r);l>0&&i.insert(i.createPositionAt(r,"end"),i.createContainerElement("thead",null,i.createSlot((e=>e.is("element","tableRow")&&e.index<l)))),l<e.getRows(n)&&i.insert(i.createPositionAt(r,"end"),i.createContainerElement("tbody",null,i.createSlot((e=>e.is("element","tableRow")&&e.index>=l))));for(const{positionOffset:e,filter:t}of o.additionalSlots)i.insert(i.createPositionAt(r,e),i.createSlot(t));return i.insert(i.createPositionAt(r,"after"),i.createSlot((e=>!e.is("element","tableRow")&&!o.additionalSlots.some((({filter:t})=>t(e)))))),o.asWidget?function(e,o){return o.setCustomProperty("table",!0,e),(0,t.toWidget)(e,o,{hasSelectionHandle:!0})}(a,i):a}}function y(e={}){return(o,{writer:n})=>{const i=o.parent,l=i.parent,r=l.getChildIndex(i),a=new _(l,{row:r}),s=l.getAttribute("headingRows")||0,c=l.getAttribute("headingColumns")||0;let d=null;for(const i of a)if(i.cell==o){const o=i.row<s||i.column<c?"th":"td";d=e.asWidget?(0,t.toWidgetEditable)(n.createEditableElement(o),n,{withAriaRole:!1}):n.createContainerElement(o);break}return d}}function v(e={}){return(t,{writer:o})=>{if(!t.parent.is("element","tableCell"))return null;if(!A(t))return null;if(e.asWidget)return o.createContainerElement("span",{class:"ck-table-bogus-paragraph"});{const e=o.createContainerElement("p");return o.setCustomProperty("dataPipeline:transparentRendering",!0,e),e}}}function A(e){return 1==e.parent.childCount&&!function(e){for(const t of e.getAttributeKeys())if(!t.startsWith("selection:")&&"htmlEmptyBlock"!=t)return!0;return!1}(e)}class T extends e.Command{refresh(){const e=this.editor.model,t=e.document.selection,o=e.schema;this.isEnabled=function(e,t){const o=e.getFirstPosition().parent,n=o===o.root?o:o.parent;return t.checkChild(n,"table")}(t,o)}execute(e={}){const t=this.editor,o=t.model,n=t.plugins.get("TableUtils"),i=t.config.get("table.defaultHeadings.rows"),l=t.config.get("table.defaultHeadings.columns");void 0===e.headingRows&&i&&(e.headingRows=i),void 0===e.headingColumns&&l&&(e.headingColumns=l),o.change((t=>{const i=n.createTable(t,e);o.insertObject(i,null,null,{findOptimalPosition:"auto"}),t.setSelection(t.createPositionAt(i.getNodeByPath([0,0,0]),0))}))}}class x extends e.Command{order;constructor(e,t={}){super(e),this.order=t.order||"below"}refresh(){const e=this.editor.model.document.selection,t=!!this.editor.plugins.get("TableUtils").getSelectionAffectedTableCells(e).length;this.isEnabled=t}execute(){const e=this.editor,t=e.model.document.selection,o=e.plugins.get("TableUtils"),n="above"===this.order,i=o.getSelectionAffectedTableCells(t),l=o.getRowIndexes(i),r=n?l.first:l.last,a=i[0].findAncestor("table");o.insertRows(a,{at:n?r:r+1,copyStructureFromAbove:!n})}}class S extends e.Command{order;constructor(e,t={}){super(e),this.order=t.order||"right"}refresh(){const e=this.editor.model.document.selection,t=!!this.editor.plugins.get("TableUtils").getSelectionAffectedTableCells(e).length;this.isEnabled=t}execute(){const e=this.editor,t=e.model.document.selection,o=e.plugins.get("TableUtils"),n="left"===this.order,i=o.getSelectionAffectedTableCells(t),l=o.getColumnIndexes(i),r=n?l.first:l.last,a=i[0].findAncestor("table");o.insertColumns(a,{columns:1,at:n?r:r+1})}}class V extends e.Command{direction;constructor(e,t={}){super(e),this.direction=t.direction||"horizontally"}refresh(){const e=this.editor.plugins.get("TableUtils").getSelectionAffectedTableCells(this.editor.model.document.selection);this.isEnabled=1===e.length}execute(){const e=this.editor.plugins.get("TableUtils"),t=e.getSelectionAffectedTableCells(this.editor.model.document.selection)[0];"horizontally"===this.direction?e.splitCellHorizontally(t,2):e.splitCellVertically(t,2)}}function R(e,t,o){const{startRow:n,startColumn:i,endRow:l,endColumn:r}=t,a=o.createElement("table"),s=e.getAttribute("tableType");s&&o.setAttribute("tableType",s,a);const c=l-n+1;for(let e=0;e<c;e++)o.insertElement("tableRow",a,"end");const d=[...new _(e,{startRow:n,endRow:l,startColumn:i,endColumn:r,includeAllSlots:!0})];for(const{row:e,column:t,cell:s,isAnchor:c,cellAnchorRow:u,cellAnchorColumn:h}of d){const d=e-n,m=a.getChild(d);if(c){const n=o.cloneElement(s);o.append(n,m),B(n,e,t,l,r,o)}else(u<n||h<i)&&b(o,o.createPositionAt(m,"end"))}return function(e,t,o,n,i){const l=parseInt(t.getAttribute("headingRows")||"0");if(l>0){u("headingRows",l-o,e,i,0)}const r=parseInt(t.getAttribute("headingColumns")||"0");if(r>0){u("headingColumns",r-n,e,i,0)}}(a,e,n,i,o),a}function I(e,t,o=0){const n=[],i=new _(e,{startRow:o,endRow:t-1});for(const e of i){const{row:o,cellHeight:i}=e;o<t&&t<=o+i-1&&n.push(e)}return n}function P(e,t,o){const n=e.parent,i=n.parent,l=n.index,r=t-l,a={},s=parseInt(e.getAttribute("rowspan"))-r;s>1&&(a.rowspan=s);const c=parseInt(e.getAttribute("colspan")||"1");c>1&&(a.colspan=c);const d=l+r,h=[...new _(i,{startRow:l,endRow:d,includeAllSlots:!0})];let m,g=null;for(const t of h){const{row:n,column:i,cell:l}=t;l===e&&void 0===m&&(m=i),void 0!==m&&m===i&&n===d&&(g=b(o,t.getPositionBefore(),a))}return u("rowspan",r,e,o),g}function E(e,t){const o=[],n=new _(e);for(const e of n){const{column:n,cellWidth:i}=e;n<t&&t<=n+i-1&&o.push(e)}return o}function L(e,t,o,n){const i=o-t,l={},r=parseInt(e.getAttribute("colspan"))-i;r>1&&(l.colspan=r);const a=parseInt(e.getAttribute("rowspan")||"1");a>1&&(l.rowspan=a);const s=b(n,n.createPositionAfter(e),l);return u("colspan",i,e,n),s}function B(e,t,o,n,i,l){const r=parseInt(e.getAttribute("colspan")||"1"),a=parseInt(e.getAttribute("rowspan")||"1");if(o+r-1>i){u("colspan",i-o+1,e,l,1)}if(t+a-1>n){u("rowspan",n-t+1,e,l,1)}}function D(e,t){const o=t.getColumns(e),n=new Array(o).fill(0);for(const{column:t}of new _(e))n[t]++;const i=n.reduce(((e,t,o)=>t?e:[...e,o]),[]);if(i.length>0){const o=i[i.length-1];return t.removeColumns(e,{at:o}),!0}return!1}function W(e,t){const o=[],n=t.getRows(e);for(let t=0;t<n;t++){e.getChild(t).isEmpty&&o.push(t)}if(o.length>0){const n=o[o.length-1];return t.removeRows(e,{at:n}),!0}return!1}function z(e,t){D(e,t)||W(e,t)}function F(e,t){const o=Array.from(new _(e,{startColumn:t.firstColumn,endColumn:t.lastColumn,row:t.lastRow}));if(o.every((({cellHeight:e})=>1===e)))return t.lastRow;const n=o[0].cellHeight-1;return t.lastRow+n}function O(e,t){const o=Array.from(new _(e,{startRow:t.firstRow,endRow:t.lastRow,column:t.lastColumn}));if(o.every((({cellWidth:e})=>1===e)))return t.lastColumn;const n=o[0].cellWidth-1;return t.lastColumn+n}class N extends e.Command{direction;isHorizontal;constructor(e,t){super(e),this.direction=t.direction,this.isHorizontal="right"==this.direction||"left"==this.direction}refresh(){const e=this._getMergeableCell();this.value=e,this.isEnabled=!!e}execute(){const e=this.editor.model,t=e.document,o=this.editor.plugins.get("TableUtils").getTableCellsContainingSelection(t.selection)[0],n=this.value,i=this.direction;e.change((e=>{const t="right"==i||"down"==i,l=t?o:n,r=t?n:o,a=r.parent;!function(e,t,o){M(e)||(M(t)&&o.remove(o.createRangeIn(t)),o.move(o.createRangeIn(e),o.createPositionAt(t,"end")));o.remove(e)}(r,l,e);const s=this.isHorizontal?"colspan":"rowspan",c=parseInt(o.getAttribute(s)||"1"),d=parseInt(n.getAttribute(s)||"1");e.setAttribute(s,c+d,l),e.setSelection(e.createRangeIn(l));const u=this.editor.plugins.get("TableUtils");z(a.findAncestor("table"),u)}))}_getMergeableCell(){const e=this.editor.model.document,t=this.editor.plugins.get("TableUtils"),o=t.getTableCellsContainingSelection(e.selection)[0];if(!o)return;const n=this.isHorizontal?function(e,t,o){const n=e.parent,i=n.parent,l="right"==t?e.nextSibling:e.previousSibling,r=(i.getAttribute("headingColumns")||0)>0;if(!l)return;const a="right"==t?e:l,s="right"==t?l:e,{column:c}=o.getCellLocation(a),{column:d}=o.getCellLocation(s),u=parseInt(a.getAttribute("colspan")||"1"),b=h(o,a),m=h(o,s);if(r&&b!=m)return;return c+u===d?l:void 0}(o,this.direction,t):function(e,t,o){const n=e.parent,i=n.parent,l=i.getChildIndex(n);if("down"==t&&l===o.getRows(i)-1||"up"==t&&0===l)return null;const r=parseInt(e.getAttribute("rowspan")||"1"),a=i.getAttribute("headingRows")||0,s="down"==t&&l+r===a,c="up"==t&&l===a;if(a&&(s||c))return null;const d=parseInt(e.getAttribute("rowspan")||"1"),u="down"==t?l+d:l,b=[...new _(i,{endRow:u})],h=b.find((t=>t.cell===e)),m=h.column,g=b.find((({row:e,cellHeight:o,column:n})=>n===m&&("down"==t?e===u:u===e+o)));return g&&g.cell?g.cell:null}(o,this.direction,t);if(!n)return;const i=this.isHorizontal?"rowspan":"colspan",l=parseInt(o.getAttribute(i)||"1");return parseInt(n.getAttribute(i)||"1")===l?n:void 0}}function M(e){const t=e.getChild(0);return 1==e.childCount&&t.is("element","paragraph")&&t.isEmpty}class H extends e.Command{refresh(){const e=this.editor.plugins.get("TableUtils"),t=e.getSelectionAffectedTableCells(this.editor.model.document.selection),o=t[0];if(o){const n=o.findAncestor("table"),i=e.getRows(n)-1,l=e.getRowIndexes(t),r=0===l.first&&l.last===i;this.isEnabled=!r}else this.isEnabled=!1}execute(){const e=this.editor.model,t=this.editor.plugins.get("TableUtils"),o=t.getSelectionAffectedTableCells(e.document.selection),n=t.getRowIndexes(o),i=o[0],l=i.findAncestor("table"),r=t.getCellLocation(i).column;e.change((e=>{const o=n.last-n.first+1;t.removeRows(l,{at:n.first,rows:o});const i=function(e,t,o,n){const i=e.getChild(Math.min(t,n-1));let l=i.getChild(0),r=0;for(const e of i.getChildren()){if(r>o)return l;l=e,r+=parseInt(e.getAttribute("colspan")||"1")}return l}(l,n.first,r,t.getRows(l));e.setSelection(e.createPositionAt(i,0))}))}}class j extends e.Command{refresh(){const e=this.editor.plugins.get("TableUtils"),t=e.getSelectionAffectedTableCells(this.editor.model.document.selection),o=t[0];if(o){const n=o.findAncestor("table"),i=e.getColumns(n),{first:l,last:r}=e.getColumnIndexes(t);this.isEnabled=r-l<i-1}else this.isEnabled=!1}execute(){const e=this.editor.plugins.get("TableUtils"),[t,o]=function(e,t){const o=t.getSelectionAffectedTableCells(e),n=o[0],i=o.pop(),l=[n,i];return n.isBefore(i)?l:l.reverse()}(this.editor.model.document.selection,e),n=t.parent.parent,i=[...new _(n)],l={first:i.find((e=>e.cell===t)).column,last:i.find((e=>e.cell===o)).column},r=function(e,t,o,n){const i=parseInt(o.getAttribute("colspan")||"1");return i>1?o:t.previousSibling||o.nextSibling?o.nextSibling||t.previousSibling:n.first?e.reverse().find((({column:e})=>e<n.first)).cell:e.reverse().find((({column:e})=>e>n.last)).cell}(i,t,o,l);this.editor.model.change((t=>{const o=l.last-l.first+1;e.removeColumns(n,{at:l.first,columns:o}),t.setSelection(t.createPositionAt(r,0))}))}}class U extends e.Command{refresh(){const e=this.editor.plugins.get("TableUtils"),t=this.editor.model,o=e.getSelectionAffectedTableCells(t.document.selection);if(0===o.length)return this.isEnabled=!1,void(this.value=!1);const n=o[0].findAncestor("table");this.isEnabled=t.schema.checkAttribute(n,"headingRows"),this.value=o.every((e=>this._isInHeading(e,e.parent.parent)))}execute(e={}){if(e.forceValue===this.value)return;const t=this.editor.plugins.get("TableUtils"),o=this.editor.model,n=t.getSelectionAffectedTableCells(o.document.selection),i=n[0].findAncestor("table"),{first:l,last:r}=t.getRowIndexes(n),a=this.value?l:r+1,s=i.getAttribute("headingRows")||0;o.change((e=>{if(a){const t=I(i,a,a>s?s:0);for(const{cell:o}of t)P(o,a,e)}u("headingRows",a,i,e,0)}))}_isInHeading(e,t){const o=parseInt(t.getAttribute("headingRows")||"0");return!!o&&e.parent.index<o}}class $ extends e.Command{refresh(){const e=this.editor.plugins.get("TableUtils"),t=this.editor.model,o=e.getSelectionAffectedTableCells(t.document.selection);if(0===o.length)return this.isEnabled=!1,void(this.value=!1);const n=o[0].findAncestor("table");this.isEnabled=t.schema.checkAttribute(n,"headingColumns"),this.value=o.every((t=>h(e,t)))}execute(e={}){if(e.forceValue===this.value)return;const t=this.editor.plugins.get("TableUtils"),o=this.editor.model,n=t.getSelectionAffectedTableCells(o.document.selection),i=n[0].findAncestor("table"),{first:l,last:r}=t.getColumnIndexes(n),a=this.value?l:r+1;o.change((e=>{if(a){const t=E(i,a);for(const{cell:o,column:n}of t)L(o,n,a,e)}u("headingColumns",a,i,e,0)}))}}function K(e,t){return 4e3/q(e,t)}function q(e,t){const o=G(e,"tbody",t)||G(e,"thead",t);return J(t.editing.view.domConverter.mapViewToDom(o))}function G(e,t,o){return[...[...o.editing.mapper.toViewElement(e).getChildren()].find((e=>e.is("element","table"))).getChildren()].find((e=>e.is("element",t)))}function J(e){const t=i.global.window.getComputedStyle(e);return"border-box"===t.boxSizing?parseFloat(t.width)-parseFloat(t.paddingLeft)-parseFloat(t.paddingRight)-parseFloat(t.borderLeftWidth)-parseFloat(t.borderRightWidth):parseFloat(t.width)}function X(e){const t=Math.pow(10,2),o="number"==typeof e?e:parseFloat(e);return Math.round(o*t)/t}function Y(e){return e.map((e=>"number"==typeof e?e:parseFloat(e))).filter((e=>!Number.isNaN(e))).reduce(((e,t)=>e+t),0)}function Q(e){let t=function(e){const t=e.filter((e=>"auto"===e)).length;if(0===t)return e.map((e=>X(e)));const o=Y(e),n=Math.max((100-o)/t,5);return e.map((e=>"auto"===e?n:e)).map((e=>X(e)))}(e.map((e=>"auto"===e?e:parseFloat(e.replace("%","")))));const o=Y(t);return 100!==o&&(t=t.map((e=>X(100*e/o))).map(((e,t,o)=>{if(!(t===o.length-1))return e;return X(e+100-Y(o))}))),t.map((e=>e+"%"))}function Z(e){const t=i.global.window.getComputedStyle(e);return"border-box"===t.boxSizing?parseInt(t.width):parseFloat(t.width)+parseFloat(t.paddingLeft)+parseFloat(t.paddingRight)+parseFloat(t.borderWidth)}function ee(e,t,o,n){for(let i=0;i<Math.max(o.length,e.length);i++){const l=e[i],r=o[i];r?l?n.setAttribute("columnWidth",r,l):n.appendElement("tableColumn",{columnWidth:r},t):n.remove(l)}}function te(e){if(e.is("element","tableColumnGroup"))return e;const t=e.getChildren();return Array.from(t).find((e=>e.is("element","tableColumnGroup")))}function oe(e){const t=te(e);return t?Array.from(t.getChildren()):[]}class ne extends e.Plugin{static get pluginName(){return"TableUtils"}static get isOfficialPlugin(){return!0}init(){this.decorate("insertColumns"),this.decorate("insertRows")}getCellLocation(e){const t=e.parent,o=t.parent,n=o.getChildIndex(t),i=new _(o,{row:n});for(const{cell:t,row:o,column:n}of i)if(t===e)return{row:o,column:n}}createTable(e,t){const o=e.createElement("table"),n=t.rows||2,i=t.columns||2;return ie(e,o,0,n,i),t.headingRows&&u("headingRows",Math.min(t.headingRows,n),o,e,0),t.headingColumns&&u("headingColumns",Math.min(t.headingColumns,i),o,e,0),o}insertRows(e,t={}){const o=this.editor.model,n=t.at||0,l=t.rows||1,r=void 0!==t.copyStructureFromAbove,a=t.copyStructureFromAbove?n-1:n,s=this.getRows(e),c=this.getColumns(e);if(n>s)throw new i.CKEditorError("tableutils-insertrows-insert-out-of-range",this,{options:t});o.change((t=>{const o=e.getAttribute("headingRows")||0;if(o>n&&u("headingRows",o+l,e,t,0),!r&&(0===n||n===s))return void ie(t,e,n,l,c);const i=r?Math.max(n,a):n,d=new _(e,{endRow:i}),h=new Array(c).fill(1);for(const{row:e,column:o,cellHeight:i,cellWidth:s,cell:c}of d){const d=e+i-1,u=e<=a&&a<=d;e<n&&n<=d?(t.setAttribute("rowspan",i+l,c),h[o]=-s):r&&u&&(h[o]=s)}for(let o=0;o<l;o++){const o=t.createElement("tableRow");t.insert(o,e,n);for(let e=0;e<h.length;e++){const n=h[e],i=t.createPositionAt(o,"end");n>0&&b(t,i,n>1?{colspan:n}:void 0),e+=Math.abs(n)-1}}}))}insertColumns(e,t={}){const o=this.editor.model,n=t.at||0,i=t.columns||1;o.change((t=>{const o=e.getAttribute("headingColumns");n<o&&t.setAttribute("headingColumns",o+i,e);const l=this.getColumns(e);if(0===n||l===n){for(const o of e.getChildren())o.is("element","tableRow")&&le(i,t,t.createPositionAt(o,n?"end":0));return}const r=new _(e,{column:n,includeAllSlots:!0});for(const e of r){const{row:o,cell:l,cellAnchorColumn:a,cellAnchorRow:s,cellWidth:c,cellHeight:d}=e;if(a<n){t.setAttribute("colspan",c+i,l);const e=s+d-1;for(let t=o;t<=e;t++)r.skipRow(t)}else le(i,t,e.getPositionBefore())}}))}removeRows(e,t){const o=this.editor.model,n=t.rows||1,l=this.getRows(e),r=t.at,a=r+n-1;if(a>l-1)throw new i.CKEditorError("tableutils-removerows-row-index-out-of-range",this,{table:e,options:t});o.change((t=>{const o={first:r,last:a},{cellsToMove:n,cellsToTrim:i}=function(e,{first:t,last:o}){const n=new Map,i=[];for(const{row:l,column:r,cellHeight:a,cell:s}of new _(e,{endRow:o})){const e=l+a-1;if(l>=t&&l<=o&&e>o){const e=a-(o-l+1);n.set(r,{cell:s,rowspan:e})}if(l<t&&e>=t){let n;n=e>=o?o-t+1:e-t+1,i.push({cell:s,rowspan:a-n})}}return{cellsToMove:n,cellsToTrim:i}}(e,o);if(n.size){!function(e,t,o,n){const i=new _(e,{includeAllSlots:!0,row:t}),l=[...i],r=e.getChild(t);let a;for(const{column:e,cell:t,isAnchor:i}of l)if(o.has(e)){const{cell:t,rowspan:i}=o.get(e),l=a?n.createPositionAfter(a):n.createPositionAt(r,0);n.move(n.createRangeOn(t),l),u("rowspan",i,t,n),a=t}else i&&(a=t)}(e,a+1,n,t)}for(let o=a;o>=r;o--)t.remove(e.getChild(o));for(const{rowspan:e,cell:o}of i)u("rowspan",e,o,t);!function(e,{first:t,last:o},n){const i=e.getAttribute("headingRows")||0;if(t<i){u("headingRows",o<i?i-(o-t+1):t,e,n,0)}}(e,o,t),D(e,this)||W(e,this)}))}removeColumns(e,t){const o=this.editor.model,n=t.at,i=t.columns||1,l=t.at+i-1;o.change((t=>{!function(e,t,o){const n=e.getAttribute("headingColumns")||0;if(n&&t.first<n){const i=Math.min(n-1,t.last)-t.first+1;o.setAttribute("headingColumns",n-i,e)}}(e,{first:n,last:l},t);const o=oe(e);for(let i=l;i>=n;i--){for(const{cell:o,column:n,cellWidth:l}of[...new _(e)])n<=i&&l>1&&n+l>i?u("colspan",l-1,o,t):n===i&&t.remove(o);if(o[i]){const e=0===i?o[1]:o[i-1],n=parseFloat(o[i].getAttribute("columnWidth")),l=parseFloat(e.getAttribute("columnWidth"));t.remove(o[i]),t.setAttribute("columnWidth",n+l+"%",e)}}W(e,this)||D(e,this)}))}splitCellVertically(e,t=2){const o=this.editor.model,n=e.parent.parent,i=parseInt(e.getAttribute("rowspan")||"1"),l=parseInt(e.getAttribute("colspan")||"1");o.change((o=>{if(l>1){const{newCellsSpan:n,updatedSpan:r}=re(l,t);u("colspan",r,e,o);const a={};n>1&&(a.colspan=n),i>1&&(a.rowspan=i);le(l>t?t-1:l-1,o,o.createPositionAfter(e),a)}if(l<t){const r=t-l,a=[...new _(n)],{column:s}=a.find((({cell:t})=>t===e)),c=a.filter((({cell:t,cellWidth:o,column:n})=>t!==e&&n===s||n<s&&n+o>s));for(const{cell:e,cellWidth:t}of c)o.setAttribute("colspan",t+r,e);const d={};i>1&&(d.rowspan=i),le(r,o,o.createPositionAfter(e),d);const b=n.getAttribute("headingColumns")||0;b>s&&u("headingColumns",b+r,n,o)}}))}splitCellHorizontally(e,t=2){const o=this.editor.model,n=e.parent,i=n.parent,l=i.getChildIndex(n),r=parseInt(e.getAttribute("rowspan")||"1"),a=parseInt(e.getAttribute("colspan")||"1");o.change((o=>{if(r>1){const n=[...new _(i,{startRow:l,endRow:l+r-1,includeAllSlots:!0})],{newCellsSpan:s,updatedSpan:c}=re(r,t);u("rowspan",c,e,o);const{column:d}=n.find((({cell:t})=>t===e)),b={};s>1&&(b.rowspan=s),a>1&&(b.colspan=a);let h=0;for(const e of n){const{column:t,row:n}=e,i=t===d;h>=s&&i&&(h=0),n>=l+c&&i&&(h||le(1,o,e.getPositionBefore(),b),h++)}}if(r<t){const n=t-r,s=[...new _(i,{startRow:0,endRow:l})];for(const{cell:t,cellHeight:i,row:r}of s)if(t!==e&&r+i>l){const e=i+n;o.setAttribute("rowspan",e,t)}const c={};a>1&&(c.colspan=a),ie(o,i,l+1,n,1,c);const d=i.getAttribute("headingRows")||0;d>l&&u("headingRows",d+n,i,o)}}))}getColumns(e){return[...e.getChild(0).getChildren()].filter((e=>e.is("element","tableCell"))).reduce(((e,t)=>e+parseInt(t.getAttribute("colspan")||"1")),0)}getRows(e){return Array.from(e.getChildren()).reduce(((e,t)=>t.is("element","tableRow")?e+1:e),0)}createTableWalker(e,t={}){return new _(e,t)}getSelectedTableCells(e){const t=[];for(const o of this.sortRanges(e.getRanges())){const e=o.getContainedElement();e&&e.is("element","tableCell")&&t.push(e)}return t}getTableCellsContainingSelection(e){const t=[];for(const o of e.getRanges()){const e=o.start.findAncestor("tableCell");e&&t.push(e)}return t}getSelectionAffectedTableCells(e){const t=this.getSelectedTableCells(e);return t.length?t:this.getTableCellsContainingSelection(e)}getRowIndexes(e){const t=e.map((e=>e.parent.index));return this._getFirstLastIndexesObject(t)}getColumnIndexes(e){const t=e[0].findAncestor("table"),o=[...new _(t)].filter((t=>e.includes(t.cell))).map((e=>e.column));return this._getFirstLastIndexesObject(o)}isSelectionRectangular(e){if(e.length<2||!this._areCellInTheSameTableSection(e))return!1;const t=new Set,o=new Set;let n=0;for(const i of e){const{row:e,column:l}=this.getCellLocation(i),r=parseInt(i.getAttribute("rowspan"))||1,a=parseInt(i.getAttribute("colspan"))||1;t.add(e),o.add(l),r>1&&t.add(e+r-1),a>1&&o.add(l+a-1),n+=r*a}const i=function(e,t){const o=Array.from(e.values()),n=Array.from(t.values()),i=Math.max(...o),l=Math.min(...o),r=Math.max(...n),a=Math.min(...n);return(i-l+1)*(r-a+1)}(t,o);return i==n}sortRanges(e){return Array.from(e).sort(ae)}_getFirstLastIndexesObject(e){const t=e.sort(((e,t)=>e-t));return{first:t[0],last:t[t.length-1]}}_areCellInTheSameTableSection(e){const t=e[0].findAncestor("table"),o=this.getRowIndexes(e),n=parseInt(t.getAttribute("headingRows"))||0;if(!this._areIndexesInSameSection(o,n))return!1;const i=this.getColumnIndexes(e),l=parseInt(t.getAttribute("headingColumns"))||0;return this._areIndexesInSameSection(i,l)}_areIndexesInSameSection({first:e,last:t},o){return e<o===t<o}}function ie(e,t,o,n,i,l={}){for(let r=0;r<n;r++){const n=e.createElement("tableRow");e.insert(n,t,o),le(i,e,e.createPositionAt(n,"end"),l)}}function le(e,t,o,n={}){for(let i=0;i<e;i++)b(t,o,n)}function re(e,t){if(e<t)return{newCellsSpan:1,updatedSpan:1};const o=Math.floor(e/t);return{newCellsSpan:o,updatedSpan:e-o*t+o}}function ae(e,t){const o=e.start,n=t.start;return o.isBefore(n)?-1:1}class se extends e.Command{refresh(){const e=this.editor.plugins.get(ne),t=e.getSelectedTableCells(this.editor.model.document.selection);this.isEnabled=e.isSelectionRectangular(t)}execute(){const e=this.editor.model,t=this.editor.plugins.get(ne);e.change((o=>{const n=t.getSelectedTableCells(e.document.selection),i=n.shift(),{mergeWidth:l,mergeHeight:r}=function(e,t,o){let n=0,i=0;for(const e of t){const{row:t,column:l}=o.getCellLocation(e);n=ue(e,l,n,"colspan"),i=ue(e,t,i,"rowspan")}const{row:l,column:r}=o.getCellLocation(e),a=n-r,s=i-l;return{mergeWidth:a,mergeHeight:s}}(i,n,t);u("colspan",l,i,o),u("rowspan",r,i,o);for(const e of n)ce(e,i,o);z(i.findAncestor("table"),t),o.setSelection(i,"in")}))}}function ce(e,t,o){de(e)||(de(t)&&o.remove(o.createRangeIn(t)),o.move(o.createRangeIn(e),o.createPositionAt(t,"end"))),o.remove(e)}function de(e){const t=e.getChild(0);return 1==e.childCount&&t.is("element","paragraph")&&t.isEmpty}function ue(e,t,o,n){const i=parseInt(e.getAttribute(n)||"1");return Math.max(o,t+i)}class be extends e.Command{constructor(e){super(e),this.affectsData=!1}refresh(){const e=this.editor.plugins.get("TableUtils").getSelectionAffectedTableCells(this.editor.model.document.selection);this.isEnabled=e.length>0}execute(){const e=this.editor.model,t=this.editor.plugins.get("TableUtils"),o=t.getSelectionAffectedTableCells(e.document.selection),n=t.getRowIndexes(o),i=o[0].findAncestor("table"),l=[];for(let t=n.first;t<=n.last;t++)for(const o of i.getChild(t).getChildren())l.push(e.createRangeOn(o));e.change((e=>{e.setSelection(l)}))}}class he extends e.Command{constructor(e){super(e),this.affectsData=!1}refresh(){const e=this.editor.plugins.get("TableUtils").getSelectionAffectedTableCells(this.editor.model.document.selection);this.isEnabled=e.length>0}execute(){const e=this.editor.plugins.get("TableUtils"),t=this.editor.model,o=e.getSelectionAffectedTableCells(t.document.selection),n=o[0],i=o.pop(),l=n.findAncestor("table"),r=e.getCellLocation(n),a=e.getCellLocation(i),s=Math.min(r.column,a.column),c=Math.max(r.column,a.column),d=[];for(const e of new _(l,{startColumn:s,endColumn:c}))d.push(t.createRangeOn(e.cell));t.change((e=>{e.setSelection(d)}))}}function me(e){e.document.registerPostFixer((t=>function(e,t){const o=t.document.differ.getChanges();let n=!1;const i=new Set;for(const t of o){let o=null;"insert"==t.type&&"table"==t.name&&(o=t.position.nodeAfter),"insert"!=t.type&&"remove"!=t.type||"tableRow"!=t.name&&"tableCell"!=t.name||(o=t.position.findAncestor("table")),fe(t)&&(o=t.range.start.findAncestor("table")),o&&!i.has(o)&&(n=ge(o,e)||n,n=pe(o,e)||n,i.add(o))}return n}(t,e)))}function ge(e,t){let o=!1;const n=function(e){const t=parseInt(e.getAttribute("headingRows")||"0"),o=Array.from(e.getChildren()).reduce(((e,t)=>t.is("element","tableRow")?e+1:e),0),n=[];for(const{row:i,cell:l,cellHeight:r}of new _(e)){if(r<2)continue;const e=i<t?t:o;if(i+r>e){const t=e-i;n.push({cell:l,rowspan:t})}}return n}(e);if(n.length){o=!0;for(const e of n)u("rowspan",e.rowspan,e.cell,t,1)}return o}function pe(e,t){let o=!1;const n=function(e){const t=new Array(e.childCount).fill(0);for(const{rowIndex:o}of new _(e,{includeAllSlots:!0}))t[o]++;return t}(e),i=[];for(const[t,o]of n.entries())!o&&e.getChild(t).is("element","tableRow")&&i.push(t);if(i.length){o=!0;for(const o of i.reverse())t.remove(e.getChild(o)),n.splice(o,1)}const l=n.filter(((t,o)=>e.getChild(o).is("element","tableRow"))),r=l[0];if(!l.every((e=>e===r))){const n=l.reduce(((e,t)=>t>e?t:e),0);for(const[i,r]of l.entries()){const l=n-r;if(l){for(let o=0;o<l;o++)b(t,t.createPositionAt(e.getChild(i),"end"));o=!0}}}return o}function fe(e){if("attribute"!==e.type)return!1;const t=e.attributeKey;return"headingRows"===t||"colspan"===t||"rowspan"===t}function we(e){e.document.registerPostFixer((t=>function(e,t){const o=t.document.differ.getChanges();let n=!1;for(const t of o)"insert"==t.type&&"table"==t.name&&(n=_e(t.position.nodeAfter,e)||n),"insert"==t.type&&"tableRow"==t.name&&(n=ke(t.position.nodeAfter,e)||n),"insert"==t.type&&"tableCell"==t.name&&(n=Ce(t.position.nodeAfter,e)||n),"remove"!=t.type&&"insert"!=t.type||!ye(t)||(n=Ce(t.position.parent,e)||n);return n}(t,e)))}function _e(e,t){let o=!1;for(const n of e.getChildren())n.is("element","tableRow")&&(o=ke(n,t)||o);return o}function ke(e,t){let o=!1;for(const n of e.getChildren())o=Ce(n,t)||o;return o}function Ce(e,t){if(0==e.childCount)return t.insertElement("paragraph",e),!0;const o=Array.from(e.getChildren()).filter((e=>e.is("$text")));for(const e of o)t.wrap(t.createRangeOn(e),"paragraph");return!!o.length}function ye(e){return!!e.position.parent.is("element","tableCell")&&("insert"==e.type&&"$text"==e.name||"remove"==e.type)}function ve(e,t){if(!e.is("element","paragraph"))return!1;const o=t.toViewElement(e);return!!o&&A(e)!==o.is("element","span")}var Ae=o(591),Te=o.n(Ae),xe=o(639),Se=o.n(xe),Ve=o(128),Re=o.n(Ve),Ie=o(21),Pe=o.n(Ie),Ee=o(51),Le=o.n(Ee),Be=o(817),De={attributes:{"data-cke":!0}};De.setAttributes=Pe(),De.insert=Re().bind(null,"head"),De.domAPI=Se(),De.insertStyleElement=Le();Te()(Be.A,De);Be.A&&Be.A.locals&&Be.A.locals;class We extends e.Plugin{_additionalSlots;static get pluginName(){return"TableEditing"}static get isOfficialPlugin(){return!0}static get requires(){return[ne]}constructor(e){super(e),this._additionalSlots=[]}init(){const e=this.editor,t=e.model,o=t.schema,n=e.conversion,l=e.plugins.get(ne);o.register("table",{inheritAllFrom:"$blockObject",allowAttributes:["headingRows","headingColumns"]}),o.register("tableRow",{allowIn:"table",isLimit:!0}),o.register("tableCell",{allowContentOf:"$container",allowIn:"tableRow",allowAttributes:["colspan","rowspan"],isLimit:!0,isSelectable:!0}),n.for("upcast").add((e=>{e.on("element:figure",((e,t,o)=>{if(!o.consumable.test(t.viewItem,{name:!0,classes:"table"}))return;const n=function(e){for(const t of e.getChildren())if(t.is("element","table"))return t}(t.viewItem);if(!n||!o.consumable.test(n,{name:!0}))return;o.consumable.consume(t.viewItem,{name:!0,classes:"table"});const l=o.convertItem(n,t.modelCursor),r=(0,i.first)(l.modelRange.getItems());r?(o.convertChildren(t.viewItem,o.writer.createPositionAt(r,"end")),o.updateConversionResult(r,t)):o.consumable.revert(t.viewItem,{name:!0,classes:"table"})}))})),n.for("upcast").add(p()),n.for("editingDowncast").elementToStructure({model:{name:"table",attributes:["headingRows"]},view:C(l,{asWidget:!0,additionalSlots:this._additionalSlots})}),n.for("dataDowncast").elementToStructure({model:{name:"table",attributes:["headingRows"]},view:C(l,{additionalSlots:this._additionalSlots})}),n.for("upcast").elementToElement({model:"tableRow",view:"tr"}),n.for("upcast").add((e=>{e.on("element:tr",((e,t)=>{t.viewItem.isEmpty&&0==t.modelCursor.index&&e.stop()}),{priority:"high"})})),n.for("downcast").elementToElement({model:"tableRow",view:(e,{writer:t})=>e.isEmpty?t.createEmptyElement("tr"):t.createContainerElement("tr")}),n.for("upcast").elementToElement({model:"tableCell",view:"td"}),n.for("upcast").elementToElement({model:"tableCell",view:"th"}),n.for("upcast").add(f("td")),n.for("upcast").add(f("th")),n.for("editingDowncast").elementToElement({model:"tableCell",view:y({asWidget:!0})}),n.for("dataDowncast").elementToElement({model:"tableCell",view:y()}),n.for("editingDowncast").elementToElement({model:"paragraph",view:v({asWidget:!0}),converterPriority:"high"}),n.for("dataDowncast").elementToElement({model:"paragraph",view:v(),converterPriority:"high"}),n.for("downcast").attributeToAttribute({model:"colspan",view:"colspan"}),n.for("upcast").attributeToAttribute({model:{key:"colspan",value:ze("colspan")},view:"colspan"}),n.for("downcast").attributeToAttribute({model:"rowspan",view:"rowspan"}),n.for("upcast").attributeToAttribute({model:{key:"rowspan",value:ze("rowspan")},view:"rowspan"}),e.config.define("table.defaultHeadings.rows",0),e.config.define("table.defaultHeadings.columns",0),e.commands.add("insertTable",new T(e)),e.commands.add("insertTableRowAbove",new x(e,{order:"above"})),e.commands.add("insertTableRowBelow",new x(e,{order:"below"})),e.commands.add("insertTableColumnLeft",new S(e,{order:"left"})),e.commands.add("insertTableColumnRight",new S(e,{order:"right"})),e.commands.add("removeTableRow",new H(e)),e.commands.add("removeTableColumn",new j(e)),e.commands.add("splitTableCellVertically",new V(e,{direction:"vertically"})),e.commands.add("splitTableCellHorizontally",new V(e,{direction:"horizontally"})),e.commands.add("mergeTableCells",new se(e)),e.commands.add("mergeTableCellRight",new N(e,{direction:"right"})),e.commands.add("mergeTableCellLeft",new N(e,{direction:"left"})),e.commands.add("mergeTableCellDown",new N(e,{direction:"down"})),e.commands.add("mergeTableCellUp",new N(e,{direction:"up"})),e.commands.add("setTableColumnHeader",new $(e)),e.commands.add("setTableRowHeader",new U(e)),e.commands.add("selectTableRow",new be(e)),e.commands.add("selectTableColumn",new he(e)),me(t),we(t),this.listenTo(t.document,"change:data",(()=>{!function(e,t){const o=e.document.differ;for(const e of o.getChanges()){let o,n=!1;if("attribute"==e.type){const t=e.range.start.nodeAfter;if(!t||!t.is("element","table"))continue;if("headingRows"!=e.attributeKey&&"headingColumns"!=e.attributeKey)continue;o=t,n="headingRows"==e.attributeKey}else"tableRow"!=e.name&&"tableCell"!=e.name||(o=e.position.findAncestor("table"),n="tableRow"==e.name);if(!o)continue;const i=o.getAttribute("headingRows")||0,l=o.getAttribute("headingColumns")||0,r=new _(o);for(const e of r){const o=e.row<i||e.column<l?"th":"td",r=t.mapper.toViewElement(e.cell);r&&r.is("element")&&r.name!=o&&t.reconvertItem(n?e.cell.parent:e.cell)}}}(t,e.editing),function(e,t){const o=e.document.differ,n=new Set;for(const e of o.getChanges()){const t="attribute"==e.type?e.range.start.parent:e.position.parent;t.is("element","tableCell")&&n.add(t)}for(const e of n.values()){const o=Array.from(e.getChildren()).filter((e=>ve(e,t.mapper)));for(const e of o)t.reconvertItem(e)}}(t,e.editing)}))}registerAdditionalSlot(e){this._additionalSlots.push(e)}}function ze(e){return t=>{const o=parseInt(t.getAttribute(e));return Number.isNaN(o)||o<=0?null:o}}var Fe=o(355),Oe=o(311),Ne=o(712),Me={attributes:{"data-cke":!0}};Me.setAttributes=Pe(),Me.insert=Re().bind(null,"head"),Me.domAPI=Se(),Me.insertStyleElement=Le();Te()(Ne.A,Me);Ne.A&&Ne.A.locals&&Ne.A.locals;class He extends Oe.View{items;keystrokes;focusTracker;constructor(e){super(e);const t=this.bindTemplate;this.items=this._createGridCollection(),this.keystrokes=new i.KeystrokeHandler,this.focusTracker=new i.FocusTracker,this.set("rows",0),this.set("columns",0),this.bind("label").to(this,"columns",this,"rows",((e,t)=>`${t} × ${e}`)),this.setTemplate({tag:"div",attributes:{class:["ck"]},children:[{tag:"div",attributes:{class:["ck-insert-table-dropdown__grid"]},on:{"mouseover@.ck-insert-table-dropdown-grid-box":t.to("boxover")},children:this.items},{tag:"div",attributes:{class:["ck","ck-insert-table-dropdown__label"],"aria-hidden":!0},children:[{text:t.to("label")}]}],on:{mousedown:t.to((e=>{e.preventDefault()})),click:t.to((()=>{this.fire("execute")}))}}),this.on("boxover",((e,t)=>{const{row:o,column:n}=t.target.dataset;this.items.get(10*(parseInt(o,10)-1)+(parseInt(n,10)-1)).focus()})),this.focusTracker.on("change:focusedElement",((e,t,o)=>{if(!o)return;const{row:n,column:i}=o.dataset;this.set({rows:parseInt(n),columns:parseInt(i)})})),this.on("change:columns",(()=>this._highlightGridBoxes())),this.on("change:rows",(()=>this._highlightGridBoxes()))}render(){super.render(),(0,Oe.addKeyboardHandlingForGrid)({keystrokeHandler:this.keystrokes,focusTracker:this.focusTracker,gridItems:this.items,numberOfColumns:10,uiLanguageDirection:this.locale&&this.locale.uiLanguageDirection});for(const e of this.items)this.focusTracker.add(e.element);this.keystrokes.listenTo(this.element)}reset(){this.set({rows:1,columns:1})}focus(){this.items.get(0).focus()}focusLast(){this.items.get(0).focus()}_highlightGridBoxes(){const e=this.rows,t=this.columns;this.items.map(((o,n)=>{const i=Math.floor(n/10)<e&&n%10<t;o.set("isOn",i)}))}_createGridButton(e,t,o,n){const i=new Oe.ButtonView(e);return i.set({label:n,class:"ck-insert-table-dropdown-grid-box"}),i.extendTemplate({attributes:{"data-row":t,"data-column":o}}),i}_createGridCollection(){const e=[];for(let t=0;t<100;t++){const o=Math.floor(t/10),n=t%10,i=`${o+1} × ${n+1}`;e.push(this._createGridButton(this.locale,o+1,n+1,i))}return this.createCollection(e)}}class je extends e.Plugin{static get pluginName(){return"TableUI"}static get isOfficialPlugin(){return!0}init(){const e=this.editor,t=this.editor.t,o="ltr"===e.locale.contentLanguageDirection;e.ui.componentFactory.add("insertTable",(o=>{const n=e.commands.get("insertTable"),i=(0,Oe.createDropdown)(o);let l;return i.bind("isEnabled").to(n),i.buttonView.set({icon:Fe.IconTable,label:t("Insert table"),tooltip:!0}),i.on("change:isOpen",(()=>{l||(l=new He(o),i.panelView.children.add(l),l.delegate("execute").to(i),i.on("execute",(()=>{e.execute("insertTable",{rows:l.rows,columns:l.columns}),e.editing.view.focus()})))})),i})),e.ui.componentFactory.add("menuBar:insertTable",(o=>{const n=e.commands.get("insertTable"),i=new Oe.MenuBarMenuView(o),l=new He(o);return l.delegate("execute").to(i),i.on("change:isOpen",((e,t,o)=>{o||l.reset()})),l.on("execute",(()=>{e.execute("insertTable",{rows:l.rows,columns:l.columns}),e.editing.view.focus()})),i.buttonView.set({label:t("Table"),icon:Fe.IconTable}),i.panelView.children.add(l),i.bind("isEnabled").to(n),i})),e.ui.componentFactory.add("tableColumn",(e=>{const n=[{type:"switchbutton",model:{commandName:"setTableColumnHeader",label:t("Header column"),bindIsOn:!0}},{type:"separator"},{type:"button",model:{commandName:o?"insertTableColumnLeft":"insertTableColumnRight",label:t("Insert column left")}},{type:"button",model:{commandName:o?"insertTableColumnRight":"insertTableColumnLeft",label:t("Insert column right")}},{type:"button",model:{commandName:"removeTableColumn",label:t("Delete column")}},{type:"button",model:{commandName:"selectTableColumn",label:t("Select column")}}];return this._prepareDropdown(t("Column"),Fe.IconTableColumn,n,e)})),e.ui.componentFactory.add("tableRow",(e=>{const o=[{type:"switchbutton",model:{commandName:"setTableRowHeader",label:t("Header row"),bindIsOn:!0}},{type:"separator"},{type:"button",model:{commandName:"insertTableRowAbove",label:t("Insert row above")}},{type:"button",model:{commandName:"insertTableRowBelow",label:t("Insert row below")}},{type:"button",model:{commandName:"removeTableRow",label:t("Delete row")}},{type:"button",model:{commandName:"selectTableRow",label:t("Select row")}}];return this._prepareDropdown(t("Row"),Fe.IconTableRow,o,e)})),e.ui.componentFactory.add("mergeTableCells",(e=>{const n=[{type:"button",model:{commandName:"mergeTableCellUp",label:t("Merge cell up")}},{type:"button",model:{commandName:o?"mergeTableCellRight":"mergeTableCellLeft",label:t("Merge cell right")}},{type:"button",model:{commandName:"mergeTableCellDown",label:t("Merge cell down")}},{type:"button",model:{commandName:o?"mergeTableCellLeft":"mergeTableCellRight",label:t("Merge cell left")}},{type:"separator"},{type:"button",model:{commandName:"splitTableCellVertically",label:t("Split cell vertically")}},{type:"button",model:{commandName:"splitTableCellHorizontally",label:t("Split cell horizontally")}}];return this._prepareMergeSplitButtonDropdown(t("Merge cells"),Fe.IconTableMergeCell,n,e)}))}_prepareDropdown(e,t,o,n){const i=this.editor,l=(0,Oe.createDropdown)(n),r=this._fillDropdownWithListOptions(l,o);return l.buttonView.set({label:e,icon:t,tooltip:!0}),l.bind("isEnabled").toMany(r,"isEnabled",((...e)=>e.some((e=>e)))),this.listenTo(l,"execute",(e=>{i.execute(e.source.commandName),e.source instanceof Oe.SwitchButtonView||i.editing.view.focus()})),l}_prepareMergeSplitButtonDropdown(e,t,o,n){const i=this.editor,l=(0,Oe.createDropdown)(n,Oe.SplitButtonView),r="mergeTableCells",a=i.commands.get(r),s=this._fillDropdownWithListOptions(l,o);return l.buttonView.set({label:e,icon:t,tooltip:!0,isEnabled:!0}),l.bind("isEnabled").toMany([a,...s],"isEnabled",((...e)=>e.some((e=>e)))),this.listenTo(l.buttonView,"execute",(()=>{i.execute(r),i.editing.view.focus()})),this.listenTo(l,"execute",(e=>{i.execute(e.source.commandName),i.editing.view.focus()})),l}_fillDropdownWithListOptions(e,t){const o=this.editor,n=[],l=new i.Collection;for(const e of t)Ue(e,o,n,l);return(0,Oe.addListToDropdown)(e,l),n}}function Ue(e,t,o,n){if("button"===e.type||"switchbutton"===e.type){const n=e.model=new Oe.ViewModel(e.model),{commandName:i,bindIsOn:l}=e.model,r=t.commands.get(i);o.push(r),n.set({commandName:i}),n.bind("isEnabled").to(r),l&&n.bind("isOn").to(r,"value"),n.set({withText:!0})}n.add(e)}var $e=o(719),Ke={attributes:{"data-cke":!0}};Ke.setAttributes=Pe(),Ke.insert=Re().bind(null,"head"),Ke.domAPI=Se(),Ke.insertStyleElement=Le();Te()($e.A,Ke);$e.A&&$e.A.locals&&$e.A.locals;class qe extends e.Plugin{static get pluginName(){return"TableSelection"}static get isOfficialPlugin(){return!0}static get requires(){return[ne,ne]}init(){const e=this.editor,t=e.model,o=e.editing.view;this.listenTo(t,"deleteContent",((e,t)=>this._handleDeleteContent(e,t)),{priority:"high"}),this.listenTo(o.document,"insertText",((e,t)=>this._handleInsertTextEvent(e,t)),{priority:"high"}),this._defineSelectionConverter(),this._enablePluginDisabling()}getSelectedTableCells(){const e=this.editor.plugins.get(ne),t=this.editor.model.document.selection,o=e.getSelectedTableCells(t);return 0==o.length?null:o}getSelectionAsFragment(){const e=this.editor.plugins.get(ne),t=this.getSelectedTableCells();return t?this.editor.model.change((o=>{const n=o.createDocumentFragment(),{first:i,last:l}=e.getColumnIndexes(t),{first:r,last:a}=e.getRowIndexes(t),s=t[0].findAncestor("table");let c=a,d=l;if(e.isSelectionRectangular(t)){const e={firstColumn:i,lastColumn:l,firstRow:r,lastRow:a};c=F(s,e),d=O(s,e)}const u=R(s,{startRow:r,startColumn:i,endRow:c,endColumn:d},o);return o.insert(u,n,0),n})):null}setCellSelection(e,t){const o=this._getCellsToSelect(e,t);this.editor.model.change((e=>{e.setSelection(o.cells.map((t=>e.createRangeOn(t))),{backward:o.backward})}))}getFocusCell(){const e=[...this.editor.model.document.selection.getRanges()].pop().getContainedElement();return e&&e.is("element","tableCell")?e:null}getAnchorCell(){const e=this.editor.model.document.selection,t=(0,i.first)(e.getRanges()).getContainedElement();return t&&t.is("element","tableCell")?t:null}_defineSelectionConverter(){const e=this.editor,t=new Set;e.conversion.for("editingDowncast").add((e=>e.on("selection",((e,o,n)=>{const i=n.writer;!function(e){for(const o of t)e.removeClass("ck-editor__editable_selected",o);t.clear()}(i);const l=this.getSelectedTableCells();if(!l)return;for(const e of l){const o=n.mapper.toViewElement(e);i.addClass("ck-editor__editable_selected",o),t.add(o)}const r=n.mapper.toViewElement(l[l.length-1]);i.setSelection(r,0)}),{priority:"lowest"})))}_enablePluginDisabling(){const e=this.editor;this.on("change:isEnabled",(()=>{if(!this.isEnabled){const t=this.getSelectedTableCells();if(!t)return;e.model.change((o=>{const n=o.createPositionAt(t[0],0),i=e.model.schema.getNearestSelectionRange(n);o.setSelection(i)}))}}))}_handleDeleteContent(e,t){const o=this.editor.plugins.get(ne),n=t[0],i=t[1],l=this.editor.model,r=!i||"backward"==i.direction,a=o.getSelectedTableCells(n);a.length&&(e.stop(),l.change((e=>{const t=a[r?a.length-1:0];l.change((e=>{for(const t of a)l.deleteContent(e.createSelection(t,"in"))}));const o=l.schema.getNearestSelectionRange(e.createPositionAt(t,0));n.is("documentSelection")?e.setSelection(o):n.setTo(o)})))}_handleInsertTextEvent(e,t){const o=this.editor,n=this.getSelectedTableCells();if(!n)return;const i=o.editing.view,l=o.editing.mapper,r=n.map((e=>i.createRangeOn(l.toViewElement(e))));t.selection=i.createSelection(r),t.preventDefault()}_getCellsToSelect(e,t){const o=this.editor.plugins.get("TableUtils"),n=o.getCellLocation(e),i=o.getCellLocation(t),l=Math.min(n.row,i.row),r=Math.max(n.row,i.row),a=Math.min(n.column,i.column),s=parseInt(t.getAttribute("colspan")||"1")-1,c=Math.max(n.column,i.column+s),d=new Array(r-l+1).fill(null).map((()=>[])),u={startRow:l,endRow:r,startColumn:a,endColumn:c};for(const{row:t,cell:o}of new _(e.findAncestor("table"),u))d[t-l].push(o);const b=i.row<n.row,h=i.column<n.column;return b&&d.reverse(),h&&d.forEach((e=>e.reverse())),{cells:d.flat(),backward:b||h}}}var Ge=o(331);class Je extends e.Plugin{static get pluginName(){return"TableClipboard"}static get isOfficialPlugin(){return!0}static get requires(){return[Ge.ClipboardMarkersUtils,Ge.ClipboardPipeline,qe,ne]}init(){const e=this.editor.editing.view.document;this.listenTo(e,"copy",((e,t)=>this._onCopyCut(e,t))),this.listenTo(e,"cut",((e,t)=>this._onCopyCut(e,t))),this._listenToContentInsertion(),this.decorate("_replaceTableSlotCell")}_listenToContentInsertion(){const{editor:e}=this,t=e.plugins.get(Ge.ClipboardPipeline),o=e.plugins.get(qe);let n=!1;t.on("contentInsertion",((e,t)=>{n="paste"===t.method})),this.listenTo(e.model,"insertContent",((e,[t,i])=>{(n||null!==o.getSelectedTableCells())&&this._onInsertContent(e,t,i)}),{priority:"high"}),t.on("contentInsertion",(()=>{n=!1}),{priority:"lowest"})}_onCopyCut(e,t){const o=this.editor.editing.view,n=this.editor.plugins.get(qe),i=this.editor.plugins.get(Ge.ClipboardMarkersUtils);n.getSelectedTableCells()&&("cut"!=e.name||this.editor.model.canEditAt(this.editor.model.document.selection))&&(t.preventDefault(),e.stop(),this.editor.model.enqueueChange({isUndoable:"cut"===e.name},(()=>{const l=i._copySelectedFragmentWithMarkers(e.name,this.editor.model.document.selection,(()=>n.getSelectionAsFragment()));o.document.fire("clipboardOutput",{dataTransfer:t.dataTransfer,content:this.editor.data.toView(l),method:e.name})})))}_onInsertContent(e,t,o){if(o&&!o.is("documentSelection"))return;const n=this.editor.model,i=this.editor.plugins.get(ne),l=this.editor.plugins.get(Ge.ClipboardMarkersUtils),r=this.getTableIfOnlyTableInContent(t,n);if(!r)return;const a=i.getSelectionAffectedTableCells(n.document.selection);a.length?(e.stop(),t.is("documentFragment")?l._pasteMarkersIntoTransformedElement(t.markers,(e=>this._replaceSelectedCells(r,a,e))):this.editor.model.change((e=>{this._replaceSelectedCells(r,a,e)}))):z(r,i)}_replaceSelectedCells(e,t,o){const n=this.editor.plugins.get(ne),i={width:n.getColumns(e),height:n.getRows(e)},l=function(e,t,o,n){const i=e[0].findAncestor("table"),l=n.getColumnIndexes(e),r=n.getRowIndexes(e),a={firstColumn:l.first,lastColumn:l.last,firstRow:r.first,lastRow:r.last},s=1===e.length;s&&(a.lastRow+=t.height-1,a.lastColumn+=t.width-1,function(e,t,o,n){const i=n.getColumns(e),l=n.getRows(e);o>i&&n.insertColumns(e,{at:i,columns:o-i});t>l&&n.insertRows(e,{at:l,rows:t-l})}(i,a.lastRow+1,a.lastColumn+1,n));s||!n.isSelectionRectangular(e)?function(e,t,o){const{firstRow:n,lastRow:i,firstColumn:l,lastColumn:r}=t,a={first:n,last:i},s={first:l,last:r};Ye(e,l,a,o),Ye(e,r+1,a,o),Xe(e,n,s,o),Xe(e,i+1,s,o,n)}(i,a,o):(a.lastRow=F(i,a),a.lastColumn=O(i,a));return a}(t,i,o,n),r=l.lastRow-l.firstRow+1,a=l.lastColumn-l.firstColumn+1;e=R(e,{startRow:0,startColumn:0,endRow:Math.min(r,i.height)-1,endColumn:Math.min(a,i.width)-1},o);const s=t[0].findAncestor("table"),c=this._replaceSelectedCellsWithPasted(e,i,s,l,o);if(this.editor.plugins.get("TableSelection").isEnabled){const e=n.sortRanges(c.map((e=>o.createRangeOn(e))));o.setSelection(e)}else o.setSelection(c[0],0);return s}_replaceSelectedCellsWithPasted(e,t,o,n,i){const{width:l,height:r}=t,a=function(e,t,o){const n=new Array(o).fill(null).map((()=>new Array(t).fill(null)));for(const{column:t,row:o,cell:i}of new _(e))n[o][t]=i;return n}(e,l,r),s=[...new _(o,{startRow:n.firstRow,endRow:n.lastRow,startColumn:n.firstColumn,endColumn:n.lastColumn,includeAllSlots:!0})],c=[];let d;for(const e of s){const{row:t,column:o}=e;o===n.firstColumn&&(d=e.getPositionBefore());const s=t-n.firstRow,u=o-n.firstColumn,b=a[s%r][u%l],h=b?i.cloneElement(b):null,m=this._replaceTableSlotCell(e,h,d,i);m&&(B(m,t,o,n.lastRow,n.lastColumn,i),c.push(m),d=i.createPositionAfter(m))}const u=parseInt(o.getAttribute("headingRows")||"0"),b=parseInt(o.getAttribute("headingColumns")||"0"),h=n.firstRow<u&&u<=n.lastRow,m=n.firstColumn<b&&b<=n.lastColumn;if(h){const e=Xe(o,u,{first:n.firstColumn,last:n.lastColumn},i,n.firstRow);c.push(...e)}if(m){const e=Ye(o,b,{first:n.firstRow,last:n.lastRow},i);c.push(...e)}return c}_replaceTableSlotCell(e,t,o,n){const{cell:i,isAnchor:l}=e;return l&&n.remove(i),t?(n.insert(t,o),t):null}getTableIfOnlyTableInContent(e,t){if(!e.is("documentFragment")&&!e.is("element"))return null;if(e.is("element","table"))return e;if(1==e.childCount&&e.getChild(0).is("element","table"))return e.getChild(0);const o=t.createRangeIn(e);for(const e of o.getItems())if(e.is("element","table")){const n=t.createRange(o.start,t.createPositionBefore(e));if(t.hasContent(n,{ignoreWhitespaces:!0}))return null;const i=t.createRange(t.createPositionAfter(e),o.end);return t.hasContent(i,{ignoreWhitespaces:!0})?null:e}return null}}function Xe(e,t,o,n,i=0){if(t<1)return;return I(e,t,i).filter((({column:e,cellWidth:t})=>Qe(e,t,o))).map((({cell:e})=>P(e,t,n)))}function Ye(e,t,o,n){if(t<1)return;return E(e,t).filter((({row:e,cellHeight:t})=>Qe(e,t,o))).map((({cell:e,column:o})=>L(e,o,t,n)))}function Qe(e,t,o){const n=e+t-1,{first:i,last:l}=o;return e>=i&&e<=l||e<i&&n>=i}class Ze extends e.Plugin{static get pluginName(){return"TableKeyboard"}static get isOfficialPlugin(){return!0}static get requires(){return[qe,ne]}init(){const e=this.editor,t=e.editing.view.document,o=e.t;this.listenTo(t,"arrowKey",((...e)=>this._onArrowKey(...e)),{context:"table"}),this.listenTo(t,"tab",((...e)=>this._handleTabOnSelectedTable(...e)),{context:"figure"}),this.listenTo(t,"tab",((...e)=>this._handleTab(...e)),{context:["th","td"]}),e.accessibility.addKeystrokeInfoGroup({id:"table",label:o("Keystrokes that can be used in a table cell"),keystrokes:[{label:o("Move the selection to the next cell"),keystroke:"Tab"},{label:o("Move the selection to the previous cell"),keystroke:"Shift+Tab"},{label:o("Insert a new table row (when in the last cell of a table)"),keystroke:"Tab"},{label:o("Navigate through the table"),keystroke:[["arrowup"],["arrowright"],["arrowdown"],["arrowleft"]]}]})}_handleTabOnSelectedTable(e,t){const o=this.editor,n=o.model.document.selection.getSelectedElement();n&&n.is("element","table")&&(t.preventDefault(),t.stopPropagation(),e.stop(),o.model.change((e=>{e.setSelection(e.createRangeIn(n.getChild(0).getChild(0)))})))}_handleTab(e,t){const o=this.editor,n=this.editor.plugins.get(ne),i=this.editor.plugins.get("TableSelection"),l=o.model.document.selection,r=!t.shiftKey;let a=n.getTableCellsContainingSelection(l)[0];if(a||(a=i.getFocusCell()),!a)return;t.preventDefault(),t.stopPropagation(),e.stop();const s=a.parent,c=s.parent,d=c.getChildIndex(s),u=s.getChildIndex(a),b=0===u;if(!r&&b&&0===d)return void o.model.change((e=>{e.setSelection(e.createRangeOn(c))}));const h=u===s.childCount-1,m=d===n.getRows(c)-1;if(r&&m&&h&&(o.execute("insertTableRowBelow"),d===n.getRows(c)-1))return void o.model.change((e=>{e.setSelection(e.createRangeOn(c))}));let g;if(r&&h){const e=c.getChild(d+1);g=e.getChild(0)}else if(!r&&b){const e=c.getChild(d-1);g=e.getChild(e.childCount-1)}else g=s.getChild(u+(r?1:-1));o.model.change((e=>{e.setSelection(e.createRangeIn(g))}))}_onArrowKey(e,t){const o=this.editor,n=t.keyCode,l=(0,i.getLocalizedArrowKeyCodeDirection)(n,o.locale.contentLanguageDirection);this._handleArrowKeys(l,t.shiftKey)&&(t.preventDefault(),t.stopPropagation(),e.stop())}_handleArrowKeys(e,t){const o=this.editor.plugins.get(ne),n=this.editor.plugins.get("TableSelection"),i=this.editor.model,l=i.document.selection,r=["right","down"].includes(e),a=o.getSelectedTableCells(l);if(a.length){let o;return o=t?n.getFocusCell():r?a[a.length-1]:a[0],this._navigateFromCellInDirection(o,e,t),!0}const s=l.focus.findAncestor("tableCell");if(!s)return!1;if(!l.isCollapsed)if(t){if(l.isBackward==r&&!l.containsEntireContent(s))return!1}else{const e=l.getSelectedElement();if(!e||!i.schema.isObject(e))return!1}return!!this._isSelectionAtCellEdge(l,s,r)&&(this._navigateFromCellInDirection(s,e,t),!0)}_isSelectionAtCellEdge(e,t,o){const n=this.editor.model,i=this.editor.model.schema,l=o?e.getLastPosition():e.getFirstPosition();if(!i.getLimitElement(l).is("element","tableCell")){return n.createPositionAt(t,o?"end":0).isTouching(l)}const r=n.createSelection(l);return n.modifySelection(r,{direction:o?"forward":"backward"}),l.isEqual(r.focus)}_navigateFromCellInDirection(e,t,o=!1){const n=this.editor.model,i=e.findAncestor("table"),l=[...new _(i,{includeAllSlots:!0})],{row:r,column:a}=l[l.length-1],s=l.find((({cell:t})=>t==e));let{row:c,column:d}=s;switch(t){case"left":d--;break;case"up":c--;break;case"right":d+=s.cellWidth;break;case"down":c+=s.cellHeight}if(c<0||c>r||d<0&&c<=0||d>a&&c>=r)return void n.change((e=>{e.setSelection(e.createRangeOn(i))}));d<0?(d=o?0:a,c--):d>a&&(d=o?a:0,c++);const u=l.find((e=>e.row==c&&e.column==d)).cell,b=["right","down"].includes(t),h=this.editor.plugins.get("TableSelection");if(o&&h.isEnabled){const t=h.getAnchorCell()||e;h.setCellSelection(t,u)}else{const e=n.createPositionAt(u,b?0:"end");n.change((t=>{t.setSelection(e)}))}}}var et=o(783);class tt extends et.DomEventObserver{domEventType=["mousemove","mouseleave"];onDomEvent(e){this.fire(e.type,e)}}class ot extends e.Plugin{static get pluginName(){return"TableMouse"}static get isOfficialPlugin(){return!0}static get requires(){return[qe,ne]}init(){this.editor.editing.view.addObserver(tt),this._enableShiftClickSelection(),this._enableMouseDragSelection()}_enableShiftClickSelection(){const e=this.editor,t=e.plugins.get(ne);let o=!1;const n=e.plugins.get(qe);this.listenTo(e.editing.view.document,"mousedown",((i,l)=>{const r=e.model.document.selection;if(!this.isEnabled||!n.isEnabled)return;if(!l.domEvent.shiftKey)return;const a=n.getAnchorCell()||t.getTableCellsContainingSelection(r)[0];if(!a)return;const s=this._getModelTableCellFromDomEvent(l);s&&nt(a,s)&&(o=!0,n.setCellSelection(a,s),l.preventDefault())})),this.listenTo(e.editing.view.document,"mouseup",(()=>{o=!1})),this.listenTo(e.editing.view.document,"selectionChange",(e=>{o&&e.stop()}),{priority:"highest"})}_enableMouseDragSelection(){const e=this.editor;let t,o,n=!1,i=!1;const l=e.plugins.get(qe);this.listenTo(e.editing.view.document,"mousedown",((e,o)=>{this.isEnabled&&l.isEnabled&&(o.domEvent.shiftKey||o.domEvent.ctrlKey||o.domEvent.altKey||(t=this._getModelTableCellFromDomEvent(o)))})),this.listenTo(e.editing.view.document,"mousemove",((e,r)=>{if(!r.domEvent.buttons)return;if(!t)return;const a=this._getModelTableCellFromDomEvent(r);a&&nt(t,a)&&(o=a,n||o==t||(n=!0)),n&&(i=!0,l.setCellSelection(t,o),r.preventDefault())})),this.listenTo(e.editing.view.document,"mouseup",(()=>{n=!1,i=!1,t=null,o=null})),this.listenTo(e.editing.view.document,"selectionChange",(e=>{i&&e.stop()}),{priority:"highest"})}_getModelTableCellFromDomEvent(e){const t=e.target,o=this.editor.editing.view.createPositionAt(t,0);return this.editor.editing.mapper.toModelPosition(o).parent.findAncestor("tableCell",{includeSelf:!0})}}function nt(e,t){return e.parent.parent==t.parent.parent}var it=o(25),lt={attributes:{"data-cke":!0}};lt.setAttributes=Pe(),lt.insert=Re().bind(null,"head"),lt.domAPI=Se(),lt.insertStyleElement=Le();Te()(it.A,lt);it.A&&it.A.locals&&it.A.locals;class rt extends e.Plugin{static get requires(){return[We,je,qe,ot,Ze,Je,t.Widget]}static get pluginName(){return"Table"}static get isOfficialPlugin(){return!0}}class at extends e.Plugin{static get pluginName(){return"PlainTableOutput"}static get isOfficialPlugin(){return!0}static get requires(){return[rt]}init(){const e=this.editor;e.conversion.for("dataDowncast").elementToStructure({model:"table",view:st,converterPriority:"high"}),e.plugins.has("TableCaption")&&e.conversion.for("dataDowncast").elementToElement({model:"caption",view:(e,{writer:t})=>{if("table"===e.parent.name)return t.createContainerElement("caption")},converterPriority:"high"}),e.plugins.has("TableProperties")&&function(e){const t={"border-width":"tableBorderWidth","border-color":"tableBorderColor","border-style":"tableBorderStyle","background-color":"tableBackgroundColor"};for(const[o,n]of Object.entries(t))e.conversion.for("dataDowncast").add((e=>e.on(`attribute:${n}:table`,((e,t,n)=>{const{item:i,attributeNewValue:l}=t,{mapper:r,writer:a}=n;if(!n.consumable.consume(i,e.name))return;const s=r.toViewElement(i);l?a.setStyle(o,l,s):a.removeStyle(o,s)}),{priority:"high"})))}(e),e.conversion.for("upcast").add((e=>{e.on("element:table",((e,t,o)=>{o.consumable.consume(t.viewItem,{classes:"table"})}))}))}}function st(e,{writer:t}){const o=e.getAttribute("headingRows")||0,n=t.createSlot((e=>e.is("element","tableRow")&&e.index<o)),i=t.createSlot((e=>e.is("element","tableRow")&&e.index>=o)),l=t.createSlot((e=>!e.is("element","tableRow"))),r=t.createContainerElement("thead",null,n),a=t.createContainerElement("tbody",null,i),s=[];return o&&s.push(r),o<e.childCount&&s.push(a),t.createContainerElement("table",{class:"table"},[l,...s])}function ct(e){const t=dt(e);return t||ut(e)}function dt(e){const t=e.getSelectedElement();return t&&bt(t)?t:null}function ut(e){const t=e.getFirstPosition();if(!t)return null;let o=t.parent;for(;o;){if(o.is("element")&&bt(o))return o;o=o.parent}return null}function bt(e){return!!e.getCustomProperty("table")&&(0,t.isWidget)(e)}class ht extends e.Plugin{static get requires(){return[t.WidgetToolbarRepository]}static get pluginName(){return"TableToolbar"}static get isOfficialPlugin(){return!0}afterInit(){const e=this.editor,o=e.t,n=e.plugins.get(t.WidgetToolbarRepository),i=e.config.get("table.contentToolbar"),l=e.config.get("table.tableToolbar");i&&n.register("tableContent",{ariaLabel:o("Table toolbar"),items:i,getRelatedElement:ut}),l&&n.register("table",{ariaLabel:o("Table toolbar"),items:l,getRelatedElement:dt})}}var mt=o(770),gt={attributes:{"data-cke":!0}};gt.setAttributes=Pe(),gt.insert=Re().bind(null,"head"),gt.domAPI=Se(),gt.insertStyleElement=Le();Te()(mt.A,gt);mt.A&&mt.A.locals&&mt.A.locals;class pt extends Oe.View{options;focusTracker;focusCycler;_focusables;dropdownView;inputView;keystrokes;_stillTyping;constructor(e,t){super(e),this.set("value",""),this.set("isReadOnly",!1),this.set("isFocused",!1),this.set("isEmpty",!0),this.options=t,this.focusTracker=new i.FocusTracker,this._focusables=new Oe.ViewCollection,this.dropdownView=this._createDropdownView(),this.inputView=this._createInputTextView(),this.keystrokes=new i.KeystrokeHandler,this._stillTyping=!1,this.focusCycler=new Oe.FocusCycler({focusables:this._focusables,focusTracker:this.focusTracker,keystrokeHandler:this.keystrokes,actions:{focusPrevious:"shift + tab",focusNext:"tab"}}),this.setTemplate({tag:"div",attributes:{class:["ck","ck-input-color"]},children:[this.dropdownView,this.inputView]}),this.on("change:value",((e,t,o)=>this._setInputValue(o)))}render(){super.render(),[this.inputView,this.dropdownView.buttonView].forEach((e=>{this.focusTracker.add(e.element),this._focusables.add(e)})),this.keystrokes.listenTo(this.element)}focus(e){-1===e?this.focusCycler.focusLast():this.focusCycler.focusFirst()}destroy(){super.destroy(),this.focusTracker.destroy(),this.keystrokes.destroy()}_createDropdownView(){const e=this.locale,t=e.t,o=this.bindTemplate,n=this._createColorSelector(e),i=(0,Oe.createDropdown)(e),l=new Oe.View;return l.setTemplate({tag:"span",attributes:{class:["ck","ck-input-color__button__preview"],style:{backgroundColor:o.to("value")}},children:[{tag:"span",attributes:{class:["ck","ck-input-color__button__preview__no-color-indicator",o.if("value","ck-hidden",(e=>""!=e))]}}]}),i.buttonView.extendTemplate({attributes:{class:"ck-input-color__button"}}),i.buttonView.children.add(l),i.buttonView.label=t("Color picker"),i.buttonView.tooltip=!0,i.panelPosition="rtl"===e.uiLanguageDirection?"se":"sw",i.panelView.children.add(n),i.bind("isEnabled").to(this,"isReadOnly",(e=>!e)),i.on("change:isOpen",((e,t,o)=>{o&&(n.updateSelectedColors(),n.showColorGridsFragment())})),i}_createInputTextView(){const e=this.locale,t=new Oe.InputTextView(e);return t.extendTemplate({on:{blur:t.bindTemplate.to("blur")}}),t.value=this.value,t.bind("isReadOnly","hasError").to(this),this.bind("isFocused","isEmpty").to(t),t.on("input",(()=>{const e=t.element.value,o=this.options.colorDefinitions.find((t=>e===t.label));this._stillTyping=!0,this.value=o&&o.color||e})),t.on("blur",(()=>{this._stillTyping=!1,this._setInputValue(t.element.value)})),t.delegate("input").to(this),t}_createColorSelector(e){const t=e.t,o=this.options.defaultColorValue||"",n=t(o?"Restore default":"Remove color"),i=new Oe.ColorSelectorView(e,{colors:this.options.colorDefinitions,columns:this.options.columns,removeButtonLabel:n,colorPickerLabel:t("Color picker"),colorPickerViewConfig:!1!==this.options.colorPickerConfig&&{...this.options.colorPickerConfig,hideInput:!0}});i.appendUI(),i.on("execute",((e,t)=>{"colorPickerSaveButton"!==t.source?(this.value=t.value||o,this.fire("input"),"colorPicker"!==t.source&&(this.dropdownView.isOpen=!1)):this.dropdownView.isOpen=!1}));let l=this.value;return i.on("colorPicker:cancel",(()=>{this.value=l,this.fire("input"),this.dropdownView.isOpen=!1})),i.colorGridsFragmentView.colorPickerButtonView.on("execute",(()=>{l=this.value})),i.bind("selectedColor").to(this,"value"),i}_setInputValue(e){if(!this._stillTyping){const t=ft(e),o=this.options.colorDefinitions.find((e=>t===ft(e.color)));this.inputView.value=o?o.label:e||""}}}function ft(e){return e.replace(/([(,])\s+/g,"$1").replace(/^\s+|\s+(?=[),\s]|$)/g,"").replace(/,|\s/g," ")}const wt=e=>""===e;function _t(e){return{none:e("None"),solid:e("Solid"),dotted:e("Dotted"),dashed:e("Dashed"),double:e("Double"),groove:e("Groove"),ridge:e("Ridge"),inset:e("Inset"),outset:e("Outset")}}function kt(e){return e('The color is invalid. Try "#FF0000" or "rgb(255,0,0)" or "red".')}function Ct(e){return e('The value is invalid. Try "10px" or "2em" or simply "2".')}function yt(e){return e=e.trim().toLowerCase(),wt(e)||(0,et.isColor)(e)}function vt(e){return e=e.trim(),wt(e)||Rt(e)||(0,et.isLength)(e)||(0,et.isPercentage)(e)}function At(e){return e=e.trim(),wt(e)||Rt(e)||(0,et.isLength)(e)}function Tt(e,t){const o=new i.Collection,n=_t(e.t);for(const i in n){const l={type:"button",model:new Oe.ViewModel({_borderStyleValue:i,label:n[i],role:"menuitemradio",withText:!0})};"none"===i?l.model.bind("isOn").to(e,"borderStyle",(e=>"none"===t?!e:e===i)):l.model.bind("isOn").to(e,"borderStyle",(e=>e===i)),o.add(l)}return o}function xt(e){const{view:t,icons:o,toolbar:n,labels:i,propertyName:l,nameToValue:r,defaultValue:a}=e;for(const e in i){const s=new Oe.ButtonView(t.locale);s.set({role:"radio",isToggleable:!0,label:i[e],icon:o[e],tooltip:i[e]});const c=r?r(e):e;s.bind("isOn").to(t,l,(e=>{let t=e;return""===e&&a&&(t=a),c===t})),s.on("execute",(()=>{!a&&c&&t[l]===c?t[l]=void 0:t[l]=c})),n.items.add(s)}}const St=[{color:"hsl(0, 0%, 0%)",label:"Black"},{color:"hsl(0, 0%, 30%)",label:"Dim grey"},{color:"hsl(0, 0%, 60%)",label:"Grey"},{color:"hsl(0, 0%, 90%)",label:"Light grey"},{color:"hsl(0, 0%, 100%)",label:"White",hasBorder:!0},{color:"hsl(0, 75%, 60%)",label:"Red"},{color:"hsl(30, 75%, 60%)",label:"Orange"},{color:"hsl(60, 75%, 60%)",label:"Yellow"},{color:"hsl(90, 75%, 60%)",label:"Light green"},{color:"hsl(120, 75%, 60%)",label:"Green"},{color:"hsl(150, 75%, 60%)",label:"Aquamarine"},{color:"hsl(180, 75%, 60%)",label:"Turquoise"},{color:"hsl(210, 75%, 60%)",label:"Light blue"},{color:"hsl(240, 75%, 60%)",label:"Blue"},{color:"hsl(270, 75%, 60%)",label:"Purple"}];function Vt(e){return(t,o,n)=>{const i=new pt(t.locale,{colorDefinitions:(l=e.colorConfig,l.map((e=>({color:e.model,label:e.label,options:{hasBorder:e.hasBorder}})))),columns:e.columns,defaultColorValue:e.defaultColorValue,colorPickerConfig:e.colorPickerConfig});var l;return i.inputView.set({id:o,ariaDescribedById:n}),i.bind("isReadOnly").to(t,"isEnabled",(e=>!e)),i.bind("hasError").to(t,"errorText",(e=>!!e)),i.on("input",(()=>{t.errorText=null})),t.bind("isEmpty","isFocused").to(i),i}}function Rt(e){const t=parseFloat(e);return!Number.isNaN(t)&&e===String(t)}var It=o(922),Pt={attributes:{"data-cke":!0}};Pt.setAttributes=Pe(),Pt.insert=Re().bind(null,"head"),Pt.domAPI=Se(),Pt.insertStyleElement=Le();Te()(It.A,Pt);It.A&&It.A.locals&&It.A.locals;var Et=o(839),Lt={attributes:{"data-cke":!0}};Lt.setAttributes=Pe(),Lt.insert=Re().bind(null,"head"),Lt.domAPI=Se(),Lt.insertStyleElement=Le();Te()(Et.A,Lt);Et.A&&Et.A.locals&&Et.A.locals;var Bt=o(911),Dt={attributes:{"data-cke":!0}};Dt.setAttributes=Pe(),Dt.insert=Re().bind(null,"head"),Dt.domAPI=Se(),Dt.insertStyleElement=Le();Te()(Bt.A,Dt);Bt.A&&Bt.A.locals&&Bt.A.locals;var Wt=o(266),zt={attributes:{"data-cke":!0}};zt.setAttributes=Pe(),zt.insert=Re().bind(null,"head"),zt.domAPI=Se(),zt.insertStyleElement=Le();Te()(Wt.A,zt);Wt.A&&Wt.A.locals&&Wt.A.locals;class Ft extends Oe.View{options;focusTracker;keystrokes;children;borderStyleDropdown;borderWidthInput;borderColorInput;backgroundInput;paddingInput;widthInput;heightInput;horizontalAlignmentToolbar;verticalAlignmentToolbar;saveButtonView;cancelButtonView;_focusables;_focusCycler;constructor(e,t){super(e),this.set({borderStyle:"",borderWidth:"",borderColor:"",padding:"",backgroundColor:"",width:"",height:"",horizontalAlignment:"",verticalAlignment:""}),this.options=t;const{borderStyleDropdown:o,borderWidthInput:n,borderColorInput:l,borderRowLabel:r}=this._createBorderFields(),{backgroundRowLabel:a,backgroundInput:s}=this._createBackgroundFields(),{widthInput:c,operatorLabel:d,heightInput:u,dimensionsLabel:b}=this._createDimensionFields(),{horizontalAlignmentToolbar:h,verticalAlignmentToolbar:m,alignmentLabel:g}=this._createAlignmentFields();this.focusTracker=new i.FocusTracker,this.keystrokes=new i.KeystrokeHandler,this.children=this.createCollection(),this.borderStyleDropdown=o,this.borderWidthInput=n,this.borderColorInput=l,this.backgroundInput=s,this.paddingInput=this._createPaddingField(),this.widthInput=c,this.heightInput=u,this.horizontalAlignmentToolbar=h,this.verticalAlignmentToolbar=m;const{saveButtonView:p,cancelButtonView:f}=this._createActionButtons();this.saveButtonView=p,this.cancelButtonView=f,this._focusables=new Oe.ViewCollection,this._focusCycler=new Oe.FocusCycler({focusables:this._focusables,focusTracker:this.focusTracker,keystrokeHandler:this.keystrokes,actions:{focusPrevious:"shift + tab",focusNext:"tab"}}),this.children.add(new Oe.FormHeaderView(e,{label:this.t("Cell properties")})),this.children.add(new Oe.FormRowView(e,{labelView:r,children:[r,o,l,n],class:"ck-table-form__border-row"})),this.children.add(new Oe.FormRowView(e,{labelView:a,children:[a,s],class:"ck-table-form__background-row"})),this.children.add(new Oe.FormRowView(e,{children:[new Oe.FormRowView(e,{labelView:b,children:[b,c,d,u],class:"ck-table-form__dimensions-row"}),new Oe.FormRowView(e,{children:[this.paddingInput],class:"ck-table-cell-properties-form__padding-row"})]})),this.children.add(new Oe.FormRowView(e,{labelView:g,children:[g,h,m],class:"ck-table-cell-properties-form__alignment-row"})),this.children.add(new Oe.FormRowView(e,{children:[this.saveButtonView,this.cancelButtonView],class:"ck-table-form__action-row"})),this.setTemplate({tag:"form",attributes:{class:["ck","ck-form","ck-table-form","ck-table-cell-properties-form"],tabindex:"-1"},children:this.children})}render(){super.render(),(0,Oe.submitHandler)({view:this}),[this.borderColorInput,this.backgroundInput].forEach((e=>{this._focusCycler.chain(e.fieldView.focusCycler)})),[this.borderStyleDropdown,this.borderColorInput,this.borderWidthInput,this.backgroundInput,this.widthInput,this.heightInput,this.paddingInput,this.horizontalAlignmentToolbar,this.verticalAlignmentToolbar,this.saveButtonView,this.cancelButtonView].forEach((e=>{this._focusables.add(e),this.focusTracker.add(e.element)})),this.keystrokes.listenTo(this.element)}destroy(){super.destroy(),this.focusTracker.destroy(),this.keystrokes.destroy()}focus(){this._focusCycler.focusFirst()}_createBorderFields(){const e=this.options.defaultTableCellProperties,t={style:e.borderStyle,width:e.borderWidth,color:e.borderColor},o=Vt({colorConfig:this.options.borderColors,columns:5,defaultColorValue:t.color,colorPickerConfig:this.options.colorPickerConfig}),n=this.locale,i=this.t,l=i("Style"),r=new Oe.LabelView(n);r.text=i("Border");const a=_t(i),s=new Oe.LabeledFieldView(n,Oe.createLabeledDropdown);s.set({label:l,class:"ck-table-form__border-style"}),s.fieldView.buttonView.set({ariaLabel:l,ariaLabelledBy:void 0,isOn:!1,withText:!0,tooltip:l}),s.fieldView.buttonView.bind("label").to(this,"borderStyle",(e=>a[e||"none"])),s.fieldView.on("execute",(e=>{this.borderStyle=e.source._borderStyleValue})),s.bind("isEmpty").to(this,"borderStyle",(e=>!e)),(0,Oe.addListToDropdown)(s.fieldView,Tt(this,t.style),{role:"menu",ariaLabel:l});const c=new Oe.LabeledFieldView(n,Oe.createLabeledInputText);c.set({label:i("Width"),class:"ck-table-form__border-width"}),c.fieldView.bind("value").to(this,"borderWidth"),c.bind("isEnabled").to(this,"borderStyle",Ot),c.fieldView.on("input",(()=>{this.borderWidth=c.fieldView.element.value}));const d=new Oe.LabeledFieldView(n,o);return d.set({label:i("Color"),class:"ck-table-form__border-color"}),d.fieldView.bind("value").to(this,"borderColor"),d.bind("isEnabled").to(this,"borderStyle",Ot),d.fieldView.on("input",(()=>{this.borderColor=d.fieldView.value})),this.on("change:borderStyle",((e,o,n,i)=>{Ot(n)||(this.borderColor="",this.borderWidth=""),Ot(i)||(this.borderColor=t.color,this.borderWidth=t.width)})),{borderRowLabel:r,borderStyleDropdown:s,borderColorInput:d,borderWidthInput:c}}_createBackgroundFields(){const e=this.locale,t=this.t,o=new Oe.LabelView(e);o.text=t("Background");const n=Vt({colorConfig:this.options.backgroundColors,columns:5,defaultColorValue:this.options.defaultTableCellProperties.backgroundColor,colorPickerConfig:this.options.colorPickerConfig}),i=new Oe.LabeledFieldView(e,n);return i.set({label:t("Color"),class:"ck-table-cell-properties-form__background"}),i.fieldView.bind("value").to(this,"backgroundColor"),i.fieldView.on("input",(()=>{this.backgroundColor=i.fieldView.value})),{backgroundRowLabel:o,backgroundInput:i}}_createDimensionFields(){const e=this.locale,t=this.t,o=new Oe.LabelView(e);o.text=t("Dimensions");const n=new Oe.LabeledFieldView(e,Oe.createLabeledInputText);n.set({label:t("Width"),class:"ck-table-form__dimensions-row__width"}),n.fieldView.bind("value").to(this,"width"),n.fieldView.on("input",(()=>{this.width=n.fieldView.element.value}));const i=new Oe.View(e);i.setTemplate({tag:"span",attributes:{class:["ck-table-form__dimension-operator"]},children:[{text:"×"}]});const l=new Oe.LabeledFieldView(e,Oe.createLabeledInputText);return l.set({label:t("Height"),class:"ck-table-form__dimensions-row__height"}),l.fieldView.bind("value").to(this,"height"),l.fieldView.on("input",(()=>{this.height=l.fieldView.element.value})),{dimensionsLabel:o,widthInput:n,operatorLabel:i,heightInput:l}}_createPaddingField(){const e=this.locale,t=this.t,o=new Oe.LabeledFieldView(e,Oe.createLabeledInputText);return o.set({label:t("Padding"),class:"ck-table-cell-properties-form__padding"}),o.fieldView.bind("value").to(this,"padding"),o.fieldView.on("input",(()=>{this.padding=o.fieldView.element.value})),o}_createAlignmentFields(){const e=this.locale,t=this.t,o=new Oe.LabelView(e),n={left:Fe.IconAlignLeft,center:Fe.IconAlignCenter,right:Fe.IconAlignRight,justify:Fe.IconAlignJustify,top:Fe.IconAlignTop,middle:Fe.IconAlignMiddle,bottom:Fe.IconAlignBottom};o.text=t("Table cell text alignment");const i=new Oe.ToolbarView(e),l="rtl"===e.contentLanguageDirection;i.set({isCompact:!0,role:"radiogroup",ariaLabel:t("Horizontal text alignment toolbar")}),xt({view:this,icons:n,toolbar:i,labels:this._horizontalAlignmentLabels,propertyName:"horizontalAlignment",nameToValue:e=>{if(l){if("left"===e)return"right";if("right"===e)return"left"}return e},defaultValue:this.options.defaultTableCellProperties.horizontalAlignment});const r=new Oe.ToolbarView(e);return r.set({isCompact:!0,role:"radiogroup",ariaLabel:t("Vertical text alignment toolbar")}),xt({view:this,icons:n,toolbar:r,labels:this._verticalAlignmentLabels,propertyName:"verticalAlignment",defaultValue:this.options.defaultTableCellProperties.verticalAlignment}),{horizontalAlignmentToolbar:i,verticalAlignmentToolbar:r,alignmentLabel:o}}_createActionButtons(){const e=this.locale,t=this.t,o=new Oe.ButtonView(e),n=new Oe.ButtonView(e),i=[this.borderWidthInput,this.borderColorInput,this.backgroundInput,this.paddingInput];return o.set({label:t("Save"),icon:Fe.IconCheck,class:"ck-button-save",type:"submit",withText:!0}),o.bind("isEnabled").toMany(i,"errorText",((...e)=>e.every((e=>!e)))),n.set({label:t("Cancel"),icon:Fe.IconCancel,class:"ck-button-cancel",withText:!0}),n.delegate("execute").to(this,"cancel"),{saveButtonView:o,cancelButtonView:n}}get _horizontalAlignmentLabels(){const e=this.locale,t=this.t,o=t("Align cell text to the left"),n=t("Align cell text to the center"),i=t("Align cell text to the right"),l=t("Justify cell text");return"rtl"===e.uiLanguageDirection?{right:i,center:n,left:o,justify:l}:{left:o,center:n,right:i,justify:l}}get _verticalAlignmentLabels(){const e=this.t;return{top:e("Align cell text to the top"),middle:e("Align cell text to the middle"),bottom:e("Align cell text to the bottom")}}}function Ot(e){return"none"!==e}function Nt(e,t,{signal:o,edges:n}={}){let i,l=null;const r=null!=n&&n.includes("leading"),a=null==n||n.includes("trailing"),s=()=>{null!==l&&(e.apply(i,l),i=void 0,l=null)};let c=null;const d=()=>{null!=c&&clearTimeout(c),c=setTimeout((()=>{c=null,a&&s(),b()}),t)},u=()=>{null!==c&&(clearTimeout(c),c=null)},b=()=>{u(),i=void 0,l=null},h=function(...e){if(o?.aborted)return;i=this,l=e;const t=null==c;d(),r&&t&&s()};return h.schedule=d,h.cancel=b,h.flush=()=>{u(),s()},o?.addEventListener("abort",b,{once:!0}),h}function Mt(e,t=0,o={}){"object"!=typeof o&&(o={});const{signal:n,leading:i=!1,trailing:l=!0,maxWait:r}=o,a=Array(2);let s;i&&(a[0]="leading"),l&&(a[1]="trailing");let c=null;const d=Nt((function(...t){s=e.apply(this,t),c=null}),t,{signal:n,edges:a}),u=function(...t){if(null!=r)if(null===c)c=Date.now();else if(Date.now()-c>=r)return s=e.apply(this,t),c=Date.now(),d.cancel(),d.schedule(),s;return d.apply(this,t),s};return u.cancel=d.cancel,u.flush=()=>(d.flush(),s),u}const Ht=(()=>[Oe.BalloonPanelView.defaultPositions.northArrowSouth,Oe.BalloonPanelView.defaultPositions.northArrowSouthWest,Oe.BalloonPanelView.defaultPositions.northArrowSouthEast,Oe.BalloonPanelView.defaultPositions.southArrowNorth,Oe.BalloonPanelView.defaultPositions.southArrowNorthWest,Oe.BalloonPanelView.defaultPositions.southArrowNorthEast,Oe.BalloonPanelView.defaultPositions.viewportStickyNorth])();function jt(e,t){const o=e.plugins.get("ContextualBalloon"),n=e.editing.view.document.selection;let i;"cell"===t?ut(n)&&(i=$t(e)):ct(n)&&(i=Ut(e)),i&&o.updatePosition(i)}function Ut(e){const t=g(e.model.document.selection),o=e.editing.mapper.toViewElement(t);return{target:e.editing.view.domConverter.mapViewToDom(o),positions:Ht}}function $t(e){const t=e.editing.mapper,o=e.editing.view.domConverter,n=e.model.document.selection;if(n.rangeCount>1)return{target:()=>function(e,t){const o=t.editing.mapper,n=t.editing.view.domConverter,l=Array.from(e).map((e=>{const t=Kt(e.start),l=o.toViewElement(t);return new i.Rect(n.mapViewToDom(l))}));return i.Rect.getBoundingRect(l)}(n.getRanges(),e),positions:Ht};const l=Kt(n.getFirstPosition()),r=t.toViewElement(l);return{target:o.mapViewToDom(r),positions:Ht}}function Kt(e){return e.nodeAfter&&e.nodeAfter.is("element","tableCell")?e.nodeAfter:e.findAncestor("tableCell")}function qt(e){if(!e||(null===(t=e)||"object"!=typeof t&&"function"!=typeof t))return e;var t;const{top:o,right:n,bottom:i,left:l}=e;return o==n&&n==i&&i==l?o:void 0}function Gt(e,t){const o=parseFloat(e);return Number.isNaN(o)||String(o)!==String(e)?e:`${o}${t}`}function Jt(e,t={}){const o={borderStyle:"none",borderWidth:"",borderColor:"",backgroundColor:"",width:"",height:"",...e};return t.includeAlignmentProperty&&!o.alignment&&(o.alignment="center"),t.includePaddingProperty&&!o.padding&&(o.padding=""),t.includeVerticalAlignmentProperty&&!o.verticalAlignment&&(o.verticalAlignment="middle"),t.includeHorizontalAlignmentProperty&&!o.horizontalAlignment&&(o.horizontalAlignment=t.isRightToLeftContent?"right":"left"),o}function Xt(e,t){return Jt({borderStyle:"double",borderColor:"hsl(0, 0%, 70%)",borderWidth:"1px",...e},t)}function Yt(e,t){return Jt({borderStyle:"solid",borderColor:"hsl(0, 0%, 75%)",borderWidth:"1px",...e},t)}const Qt={borderStyle:"tableCellBorderStyle",borderColor:"tableCellBorderColor",borderWidth:"tableCellBorderWidth",height:"tableCellHeight",width:"tableCellWidth",padding:"tableCellPadding",backgroundColor:"tableCellBackgroundColor",horizontalAlignment:"tableCellHorizontalAlignment",verticalAlignment:"tableCellVerticalAlignment"};class Zt extends e.Plugin{_defaultContentTableCellProperties;_defaultLayoutTableCellProperties;_balloon;view;_viewWithContentTableDefaults;_viewWithLayoutTableDefaults;_undoStepBatch;_isReady;static get requires(){return[Oe.ContextualBalloon]}static get pluginName(){return"TableCellPropertiesUI"}static get isOfficialPlugin(){return!0}constructor(e){super(e),e.config.define("table.tableCellProperties",{borderColors:St,backgroundColors:St})}init(){const e=this.editor,t=e.t;this._defaultContentTableCellProperties=Yt(e.config.get("table.tableCellProperties.defaultProperties"),{includeVerticalAlignmentProperty:!0,includeHorizontalAlignmentProperty:!0,includePaddingProperty:!0,isRightToLeftContent:"rtl"===e.locale.contentLanguageDirection}),this._defaultLayoutTableCellProperties=Jt(void 0,{includeVerticalAlignmentProperty:!0,includeHorizontalAlignmentProperty:!0,isRightToLeftContent:"rtl"===e.locale.contentLanguageDirection}),this._balloon=e.plugins.get(Oe.ContextualBalloon),this.view=null,this._isReady=!1,e.ui.componentFactory.add("tableCellProperties",(o=>{const n=new Oe.ButtonView(o);n.set({label:t("Cell properties"),icon:Fe.IconTableCellProperties,tooltip:!0}),this.listenTo(n,"execute",(()=>this._showView()));const i=Object.values(Qt).map((t=>e.commands.get(t)));return n.bind("isEnabled").toMany(i,"isEnabled",((...e)=>e.some((e=>e)))),n}))}destroy(){super.destroy(),this.view&&this.view.destroy()}_createPropertiesView(e){const t=this.editor,o=t.config.get("table.tableCellProperties"),n=(0,Oe.normalizeColorOptions)(o.borderColors),i=(0,Oe.getLocalizedColorOptions)(t.locale,n),l=(0,Oe.normalizeColorOptions)(o.backgroundColors),r=(0,Oe.getLocalizedColorOptions)(t.locale,l),a=!1!==o.colorPicker,s=new Ft(t.locale,{borderColors:i,backgroundColors:r,defaultTableCellProperties:e,colorPickerConfig:!!a&&(o.colorPicker||{})}),c=t.t;s.render(),this.listenTo(s,"submit",(()=>{this._hideView()})),this.listenTo(s,"cancel",(()=>{this._undoStepBatch.operations.length&&t.execute("undo",this._undoStepBatch),this._hideView()})),s.keystrokes.set("Esc",((e,t)=>{this._hideView(),t()})),(0,Oe.clickOutsideHandler)({emitter:s,activator:()=>this._isViewInBalloon,contextElements:[this._balloon.view.element],callback:()=>this._hideView()});const d=kt(c),u=Ct(c);return s.on("change:borderStyle",this._getPropertyChangeCallback("tableCellBorderStyle")),s.on("change:borderColor",this._getValidatedPropertyChangeCallback({viewField:s.borderColorInput,commandName:"tableCellBorderColor",errorText:d,validator:yt})),s.on("change:borderWidth",this._getValidatedPropertyChangeCallback({viewField:s.borderWidthInput,commandName:"tableCellBorderWidth",errorText:u,validator:At})),s.on("change:padding",this._getValidatedPropertyChangeCallback({viewField:s.paddingInput,commandName:"tableCellPadding",errorText:u,validator:vt})),s.on("change:width",this._getValidatedPropertyChangeCallback({viewField:s.widthInput,commandName:"tableCellWidth",errorText:u,validator:vt})),s.on("change:height",this._getValidatedPropertyChangeCallback({viewField:s.heightInput,commandName:"tableCellHeight",errorText:u,validator:vt})),s.on("change:backgroundColor",this._getValidatedPropertyChangeCallback({viewField:s.backgroundInput,commandName:"tableCellBackgroundColor",errorText:d,validator:yt})),s.on("change:horizontalAlignment",this._getPropertyChangeCallback("tableCellHorizontalAlignment")),s.on("change:verticalAlignment",this._getPropertyChangeCallback("tableCellVerticalAlignment")),s}_fillViewFormFromCommandValues(){const e=this.editor.commands,t=e.get("tableCellBorderStyle");Object.entries(Qt).map((([t,o])=>{const n=t,i=this.view===this._viewWithContentTableDefaults?this._defaultContentTableCellProperties[n]||"":this._defaultLayoutTableCellProperties[n]||"";return[t,e.get(o).value||i]})).forEach((([e,o])=>{("borderColor"!==e&&"borderWidth"!==e||"none"!==t.value)&&this.view.set(e,o)})),this._isReady=!0}_showView(){const e=this.editor,t=ct(e.editing.view.document.selection),o=t&&e.editing.mapper.toModelElement(t),n=!o||"layout"!==o.getAttribute("tableType");n&&!this._viewWithContentTableDefaults?this._viewWithContentTableDefaults=this._createPropertiesView(this._defaultContentTableCellProperties):n||this._viewWithLayoutTableDefaults||(this._viewWithLayoutTableDefaults=this._createPropertiesView(this._defaultLayoutTableCellProperties)),this.view=n?this._viewWithContentTableDefaults:this._viewWithLayoutTableDefaults,this.listenTo(e.ui,"update",(()=>{this._updateView()})),this._fillViewFormFromCommandValues(),this._balloon.add({view:this.view,position:$t(e)}),this._undoStepBatch=e.model.createBatch(),this.view.focus()}_hideView(){const e=this.editor;this.stopListening(e.ui,"update"),this._isReady=!1,this.view.saveButtonView.focus(),this._balloon.remove(this.view),this.editor.editing.view.focus()}_updateView(){const e=this.editor;ut(e.editing.view.document.selection)?this._isViewVisible&&jt(e,"cell"):this._hideView()}get _isViewVisible(){return!!this.view&&this._balloon.visibleView===this.view}get _isViewInBalloon(){return!!this.view&&this._balloon.hasView(this.view)}_getPropertyChangeCallback(e){return(t,o,n)=>{this._isReady&&this.editor.execute(e,{value:n,batch:this._undoStepBatch})}}_getValidatedPropertyChangeCallback(e){const{commandName:t,viewField:o,validator:n,errorText:i}=e,l=Mt((()=>{o.errorText=i}),500);return(e,i,r)=>{l.cancel(),this._isReady&&(n(r)?(this.editor.execute(t,{value:r,batch:this._undoStepBatch}),o.errorText=null):l())}}}class eo extends e.Command{attributeName;_defaultValue;_defaultContentTableValue;_defaultLayoutTableValue;constructor(e,t,o){switch(super(e),this.attributeName=t,this._defaultContentTableValue=o,t){case"tableCellBorderStyle":this._defaultLayoutTableValue="none";break;case"tableCellHorizontalAlignment":this._defaultLayoutTableValue="left";break;case"tableCellVerticalAlignment":this._defaultLayoutTableValue="middle";break;default:this._defaultLayoutTableValue=void 0}}refresh(){const e=this.editor.model.document.selection,t=this.editor.plugins.get("TableUtils").getSelectionAffectedTableCells(e),o=g(e);this._defaultValue=o&&"layout"===o.getAttribute("tableType")?this._defaultLayoutTableValue:this._defaultContentTableValue,this.isEnabled=!!t.length,this.value=this._getSingleValue(t)}execute(e={}){const{value:t,batch:o}=e,n=this.editor.model,i=this.editor.plugins.get("TableUtils").getSelectionAffectedTableCells(n.document.selection),l=this._getValueToSet(t);n.enqueueChange(o,(e=>{l?i.forEach((t=>e.setAttribute(this.attributeName,l,t))):i.forEach((t=>e.removeAttribute(this.attributeName,t)))}))}_getAttribute(e){if(!e)return;const t=e.getAttribute(this.attributeName);return t!==this._defaultValue?t:void 0}_getValueToSet(e){if(e!==this._defaultValue)return e}_getSingleValue(e){const t=this._getAttribute(e[0]);return e.every((e=>this._getAttribute(e)===t))?t:void 0}}class to extends eo{constructor(e,t){super(e,"tableCellWidth",t)}_getValueToSet(e){if((e=Gt(e,"px"))!==this._defaultValue)return e}}class oo extends e.Plugin{static get pluginName(){return"TableCellWidthEditing"}static get isOfficialPlugin(){return!0}static get requires(){return[We]}init(){const e=this.editor,t=Yt(e.config.get("table.tableCellProperties.defaultProperties"));m(e.model.schema,e.conversion,{modelAttribute:"tableCellWidth",styleName:"width",defaultValue:t.width}),e.commands.add("tableCellWidth",new to(e,t.width))}}class no extends eo{constructor(e,t){super(e,"tableCellPadding",t)}_getAttribute(e){if(!e)return;const t=qt(e.getAttribute(this.attributeName));return t!==this._defaultValue?t:void 0}_getValueToSet(e){const t=Gt(e,"px");if(t!==this._defaultValue)return t}}class io extends eo{constructor(e,t){super(e,"tableCellHeight",t)}_getValueToSet(e){const t=Gt(e,"px");if(t!==this._defaultValue)return t}}class lo extends eo{constructor(e,t){super(e,"tableCellBackgroundColor",t)}}class ro extends eo{constructor(e,t){super(e,"tableCellVerticalAlignment",t)}}class ao extends eo{constructor(e,t){super(e,"tableCellHorizontalAlignment",t)}}class so extends eo{constructor(e,t){super(e,"tableCellBorderStyle",t)}_getAttribute(e){if(!e)return;const t=qt(e.getAttribute(this.attributeName));return t!==this._defaultValue?t:void 0}}class co extends eo{constructor(e,t){super(e,"tableCellBorderColor",t)}_getAttribute(e){if(!e)return;const t=qt(e.getAttribute(this.attributeName));return t!==this._defaultValue?t:void 0}}class uo extends eo{constructor(e,t){super(e,"tableCellBorderWidth",t)}_getAttribute(e){if(!e)return;const t=qt(e.getAttribute(this.attributeName));return t!==this._defaultValue?t:void 0}_getValueToSet(e){const t=Gt(e,"px");if(t!==this._defaultValue)return t}}const bo=/^(top|middle|bottom)$/,ho=/^(left|center|right|justify)$/;class mo extends e.Plugin{static get pluginName(){return"TableCellPropertiesEditing"}static get isOfficialPlugin(){return!0}static get requires(){return[We,oo]}init(){const e=this.editor,t=e.model.schema,o=e.conversion;e.config.define("table.tableCellProperties.defaultProperties",{});const n=Yt(e.config.get("table.tableCellProperties.defaultProperties"),{includeVerticalAlignmentProperty:!0,includeHorizontalAlignmentProperty:!0,includePaddingProperty:!0,isRightToLeftContent:"rtl"===e.locale.contentLanguageDirection});e.data.addStyleProcessorRules(et.addBorderRules),function(e,t,o){const n={width:"tableCellBorderWidth",color:"tableCellBorderColor",style:"tableCellBorderStyle"};e.extend("tableCell",{allowAttributes:Object.values(n)}),r(t,"td",n,o),r(t,"th",n,o),a(t,{modelElement:"tableCell",modelAttribute:n.style,styleName:"border-style"}),a(t,{modelElement:"tableCell",modelAttribute:n.color,styleName:"border-color"}),a(t,{modelElement:"tableCell",modelAttribute:n.width,styleName:"border-width"})}(t,o,{color:n.borderColor,style:n.borderStyle,width:n.borderWidth}),e.commands.add("tableCellBorderStyle",new so(e,n.borderStyle)),e.commands.add("tableCellBorderColor",new co(e,n.borderColor)),e.commands.add("tableCellBorderWidth",new uo(e,n.borderWidth)),m(t,o,{modelAttribute:"tableCellHeight",styleName:"height",defaultValue:n.height}),e.commands.add("tableCellHeight",new io(e,n.height)),e.data.addStyleProcessorRules(et.addPaddingRules),m(t,o,{modelAttribute:"tableCellPadding",styleName:"padding",reduceBoxSides:!0,defaultValue:n.padding}),e.commands.add("tableCellPadding",new no(e,n.padding)),e.data.addStyleProcessorRules(et.addBackgroundRules),m(t,o,{modelAttribute:"tableCellBackgroundColor",styleName:"background-color",defaultValue:n.backgroundColor}),e.commands.add("tableCellBackgroundColor",new lo(e,n.backgroundColor)),function(e,t,o){e.extend("tableCell",{allowAttributes:["tableCellHorizontalAlignment"]}),t.for("downcast").attributeToAttribute({model:{name:"tableCell",key:"tableCellHorizontalAlignment"},view:e=>({key:"style",value:{"text-align":e}})}),t.for("upcast").attributeToAttribute({view:{name:/^(td|th)$/,styles:{"text-align":ho}},model:{key:"tableCellHorizontalAlignment",value:(e,t,n)=>{const i=c(o,"left",n),l=e.getStyle("text-align");return l===i?null:l}}}).attributeToAttribute({view:{name:/^(td|th)$/,attributes:{align:ho}},model:{key:"tableCellHorizontalAlignment",value:(e,t,n)=>{const i=c(o,"left",n),l=e.getAttribute("align");return l===i?null:l}}})}(t,o,n.horizontalAlignment),e.commands.add("tableCellHorizontalAlignment",new ao(e,n.horizontalAlignment)),function(e,t,o){e.extend("tableCell",{allowAttributes:["tableCellVerticalAlignment"]}),t.for("downcast").attributeToAttribute({model:{name:"tableCell",key:"tableCellVerticalAlignment"},view:e=>({key:"style",value:{"vertical-align":e}})}),t.for("upcast").attributeToAttribute({view:{name:/^(td|th)$/,styles:{"vertical-align":bo}},model:{key:"tableCellVerticalAlignment",value:(e,t,n)=>{const i=c(o,"middle",n),l=e.getStyle("vertical-align");return l===i?null:l}}}).attributeToAttribute({view:{name:/^(td|th)$/,attributes:{valign:bo}},model:{key:"tableCellVerticalAlignment",value:(e,t,n)=>{const i=c(o,"middle",n),l=e.getAttribute("valign");return l===i?null:l}}})}(t,o,n.verticalAlignment),e.commands.add("tableCellVerticalAlignment",new ro(e,n.verticalAlignment))}}class go extends e.Plugin{static get pluginName(){return"TableCellProperties"}static get isOfficialPlugin(){return!0}static get requires(){return[mo,Zt]}}class po extends e.Plugin{static get pluginName(){return"TableLayoutUI"}static get isOfficialPlugin(){return!0}init(){const e=this.editor,t=this.editor.t;e.ui.componentFactory.add("insertTableLayout",(o=>{const n=e.commands.get("insertTableLayout"),i=(0,Oe.createDropdown)(o);let l;return i.bind("isEnabled").to(n),i.buttonView.set({icon:Fe.IconTableLayout,label:t("Insert table layout"),tooltip:!0}),i.on("change:isOpen",(()=>{l||(l=new He(o),i.panelView.children.add(l),l.delegate("execute").to(i),i.on("execute",(()=>{e.execute("insertTableLayout",{rows:l.rows,columns:l.columns}),e.editing.view.focus()})))})),i})),e.ui.componentFactory.add("menuBar:insertTableLayout",(o=>{const n=e.commands.get("insertTableLayout"),i=new Oe.MenuBarMenuView(o),l=new He(o);return l.delegate("execute").to(i),i.on("change:isOpen",((e,t,o)=>{o||l.reset()})),l.on("execute",(()=>{e.execute("insertTableLayout",{rows:l.rows,columns:l.columns}),e.editing.view.focus()})),i.buttonView.set({label:t("Table layout"),icon:Fe.IconTableLayout}),i.panelView.children.add(l),i.bind("isEnabled").to(n),i})),e.ui.componentFactory.add("tableType",(()=>{const e=this.editor,t=e.t,o=new Oe.DropdownButtonView(e.locale);return o.set({label:t("Table type"),icon:Fe.IconTableProperties,tooltip:!0}),fo(e,o)}))}afterInit(){const e=this.editor;if(!e.plugins.has("TablePropertiesUI"))return;const t=e.plugins.get("TablePropertiesUI");e.ui.componentFactory.add("tableProperties",(o=>{const n=t._createTablePropertiesButton(),i=new Oe.SplitButtonView(o,n);return fo(e,i)}))}}function fo(e,t){const o=e.t,n=e.locale,l=e.commands.get("tableType"),r=(0,Oe.createDropdown)(n,t),a=function(e){const t=e.t,o=e.commands.get("tableType"),n=new i.Collection;return n.add(wo(o,"layout",t("Layout table"))),n.add(wo(o,"content",t("Content table"))),n}(e);return(0,Oe.addListToDropdown)(r,a,{ariaLabel:o("Table type options"),role:"menu"}),t.tooltip=o("Choose table type"),r.on("execute",(e=>{const t=e.source.tableType;t&&l.execute(t)})),r}function wo(e,t,o){const n=new Oe.ViewModel({label:o,role:"menuitemradio",withText:!0,tableType:t});return n.bind("isEnabled").to(e,"isEnabled"),n.bind("isOn").to(e,"value",(e=>e===t)),{type:"button",model:n}}class _o extends e.Command{refresh(){const e=this.editor.model,t=e.document.selection,o=e.schema;this.isEnabled=function(e,t){const o=e.getFirstPosition().parent,n=o===o.root?o:o.parent;return t.checkChild(n,"table")}(t,o)}execute(e={}){const t=this.editor,o=t.model,n=t.plugins.get("TableUtils");o.change((i=>{const l={rows:e.rows||2,columns:e.columns||2},r=n.createTable(i,l);i.setAttribute("tableType","layout",r),o.insertObject(r,null,null,{findOptimalPosition:"auto"});const a=100/l.columns+"%",s=Array(l.columns).fill(a);t.commands.get("resizeColumnWidths").execute({tableWidth:"100%",columnWidths:s,table:r}),i.setSelection(i.createPositionAt(r.getNodeByPath([0,0,0]),0))}))}}function ko(e){if(!e||"object"!=typeof e)return!1;const t=Object.getPrototypeOf(e);return!(null!==t&&t!==Object.prototype&&null!==Object.getPrototypeOf(t))&&"[object Object]"===Object.prototype.toString.call(e)}function Co(e){return Object.getOwnPropertySymbols(e).filter((t=>Object.prototype.propertyIsEnumerable.call(e,t)))}function yo(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":Object.prototype.toString.call(e)}const vo="[object Arguments]",Ao="[object Object]";function To(e,t,o,n,i,l,r){const a=r(e,t,o,n,i,l);if(void 0!==a)return a;if(typeof e==typeof t)switch(typeof e){case"bigint":case"string":case"boolean":case"symbol":case"undefined":case"function":return e===t;case"number":return e===t||Object.is(e,t);case"object":return xo(e,t,l,r)}return xo(e,t,l,r)}function xo(e,t,o,n){if(Object.is(e,t))return!0;let i=yo(e),l=yo(t);if(i===vo&&(i=Ao),l===vo&&(l=Ao),i!==l)return!1;switch(i){case"[object String]":return e.toString()===t.toString();case"[object Number]":{const o=e.valueOf(),n=t.valueOf();return(r=o)===(a=n)||Number.isNaN(r)&&Number.isNaN(a)}case"[object Boolean]":case"[object Date]":case"[object Symbol]":return Object.is(e.valueOf(),t.valueOf());case"[object RegExp]":return e.source===t.source&&e.flags===t.flags;case"[object Function]":return e===t}var r,a;const s=(o=o??new Map).get(e),c=o.get(t);if(null!=s&&null!=c)return s===t;o.set(e,t),o.set(t,e);try{switch(i){case"[object Map]":if(e.size!==t.size)return!1;for(const[i,l]of e.entries())if(!t.has(i)||!To(l,t.get(i),i,e,t,o,n))return!1;return!0;case"[object Set]":{if(e.size!==t.size)return!1;const i=Array.from(e.values()),l=Array.from(t.values());for(let r=0;r<i.length;r++){const a=i[r],s=l.findIndex((i=>To(a,i,void 0,e,t,o,n)));if(-1===s)return!1;l.splice(s,1)}return!0}case"[object Array]":case"[object Uint8Array]":case"[object Uint8ClampedArray]":case"[object Uint16Array]":case"[object Uint32Array]":case"[object BigUint64Array]":case"[object Int8Array]":case"[object Int16Array]":case"[object Int32Array]":case"[object BigInt64Array]":case"[object Float32Array]":case"[object Float64Array]":if("undefined"!=typeof Buffer&&Buffer.isBuffer(e)!==Buffer.isBuffer(t))return!1;if(e.length!==t.length)return!1;for(let i=0;i<e.length;i++)if(!To(e[i],t[i],i,e,t,o,n))return!1;return!0;case"[object ArrayBuffer]":return e.byteLength===t.byteLength&&xo(new Uint8Array(e),new Uint8Array(t),o,n);case"[object DataView]":return e.byteLength===t.byteLength&&e.byteOffset===t.byteOffset&&xo(new Uint8Array(e),new Uint8Array(t),o,n);case"[object Error]":return e.name===t.name&&e.message===t.message;case Ao:{if(!(xo(e.constructor,t.constructor,o,n)||ko(e)&&ko(t)))return!1;const i=[...Object.keys(e),...Co(e)],l=[...Object.keys(t),...Co(t)];if(i.length!==l.length)return!1;for(let l=0;l<i.length;l++){const r=i[l],a=e[r];if(!Object.hasOwn(t,r))return!1;if(!To(a,t[r],r,e,t,o,n))return!1}return!0}default:return!1}}finally{o.delete(e),o.delete(t)}}function So(){}function Vo(e,t){return function(e,t,o){return To(e,t,void 0,void 0,void 0,void 0,o)}(e,t,So)}class Ro extends e.Command{refresh(){this.isEnabled=!0}execute(e={}){const{model:t,plugins:o}=this.editor;let{table:n=t.document.selection.getSelectedElement(),columnWidths:i,tableWidth:l}=e;i&&(i=Array.isArray(i)?i:i.split(",")),t.change((e=>{l?e.setAttribute("tableWidth",l,n):e.removeAttribute("tableWidth",n);const t=o.get("TableColumnResizeEditing").getColumnGroupElement(n);if(!i&&!t)return;if(!i)return e.remove(t);const r=Q(i);if(t)Array.from(t.getChildren()).forEach(((t,o)=>e.setAttribute("columnWidth",r[o],t)));else{const t=e.createElement("tableColumnGroup");r.forEach((o=>e.appendElement("tableColumn",{columnWidth:o},t))),e.append(t,n)}}))}}function Io(e){return t=>t.on("element:colgroup",((t,o,n)=>{const i=o.modelCursor.findAncestor("table"),l=te(i);if(!l)return;const r=oe(l),a=e.getColumns(i);let s=(c=l,d=n.writer,oe(c).reduce(((e,t)=>{const o=t.getAttribute("columnWidth"),n=t.getAttribute("colSpan");if(!n)return e.push(o),e;for(let t=0;t<n;t++)e.push(o);return d.removeAttribute("colSpan",t),e}),[]));var c,d;s=Array.from({length:a},((e,t)=>s[t]||"auto")),(s.length!=r.length||s.includes("auto"))&&ee(r,l,Q(s),n.writer)}),{priority:"low"})}const Po=(0,i.toUnit)("px");class Eo extends e.Plugin{_isResizingActive;_resizingData;_domEmitter;_tableUtilsPlugin;_initialMouseEventData=null;static get requires(){return[We,ne]}static get pluginName(){return"TableColumnResizeEditing"}static get isOfficialPlugin(){return!0}constructor(e){super(e),this._isResizingActive=!1,this.set("_isResizingAllowed",!0),this._resizingData=null,this._domEmitter=new((0,i.DomEmitterMixin)()),this._tableUtilsPlugin=e.plugins.get("TableUtils"),this.on("change:_isResizingAllowed",((t,o,n)=>{const i=n?"removeClass":"addClass";e.editing.view.change((t=>{for(const o of e.editing.view.document.roots)t[i]("ck-column-resize_disabled",e.editing.view.document.getRoot(o.rootName))}))}))}init(){this._extendSchema(),this._registerPostFixer(),this._registerConverters(),this._registerResizingListeners(),this._registerResizerInserter();const e=this.editor,t=e.plugins.get("TableColumnResize");e.plugins.get("TableEditing").registerAdditionalSlot({filter:e=>e.is("element","tableColumnGroup"),positionOffset:0});const o=new Ro(e);e.commands.add("resizeTableWidth",o),e.commands.add("resizeColumnWidths",o),this.bind("_isResizingAllowed").to(e,"isReadOnly",t,"isEnabled",o,"isEnabled",((e,t,o)=>!e&&t&&o))}destroy(){this._domEmitter.stopListening(),super.destroy()}getColumnGroupElement(e){return te(e)}getTableColumnElements(e){return oe(e)}getTableColumnsWidths(e){return function(e){return oe(e).map((e=>e.getAttribute("columnWidth")))}(e)}_extendSchema(){this.editor.model.schema.extend("table",{allowAttributes:["tableWidth"]}),this.editor.model.schema.register("tableColumnGroup",{allowIn:"table",isLimit:!0}),this.editor.model.schema.register("tableColumn",{allowIn:"tableColumnGroup",allowAttributes:["columnWidth","colSpan"],isLimit:!0})}_registerPostFixer(){const e=this.editor.model;function t(e,t,o){const n=o._tableUtilsPlugin.getColumns(t);if(0===n-e.length)return e;const i=e.map((e=>Number(e.replace("%","")))),l=function(e,t){const o=new Set;for(const n of e.getChanges())if("insert"==n.type&&n.position.nodeAfter&&"tableCell"==n.position.nodeAfter.name&&n.position.nodeAfter.getAncestors().includes(t))o.add(n.position.nodeAfter);else if("remove"==n.type){const e=n.position.nodeBefore||n.position.nodeAfter;"tableCell"==e.name&&e.getAncestors().includes(t)&&o.add(e)}return o}(o.editor.model.document.differ,t);for(const e of l){const l=n-i.length;if(0===l)continue;const a=l>0,s=o._tableUtilsPlugin.getCellLocation(e).column;if(a){const e=K(t,o.editor),n=(r=e,Array(l).fill(r));i.splice(s,0,...n)}else{const e=i.splice(s,Math.abs(l));i[s]+=Y(e)}}var r;return i.map((e=>e+"%"))}e.document.registerPostFixer((o=>{let n=!1;for(const i of function(e){const t=new Set;for(const o of e.document.differ.getChanges()){let n=null;switch(o.type){case"insert":n=["table","tableRow","tableCell"].includes(o.name)?o.position:null;break;case"remove":n=["tableRow","tableCell"].includes(o.name)?o.position:null;break;case"attribute":o.range.start.nodeAfter&&(n=["table","tableRow","tableCell"].includes(o.range.start.nodeAfter.name)?o.range.start:null)}if(!n)continue;const i=n.nodeAfter&&n.nodeAfter.is("element","table")?n.nodeAfter:n.findAncestor("table");for(const o of e.createRangeOn(i).getItems())o.is("element","table")&&te(o)&&t.add(o)}return t}(e)){const e=this.getColumnGroupElement(i),l=this.getTableColumnElements(e),r=this.getTableColumnsWidths(e);let a=Q(r);a=t(a,i,this),Vo(r,a)||(ee(l,e,a,o),n=!0)}return n}))}_registerConverters(){const e=this.editor.conversion;e.for("upcast").attributeToAttribute({view:{name:/^(figure|table)$/,styles:{width:/[\s\S]+/}},model:{name:"table",key:"tableWidth",value:e=>{if(!e.parent.is("element","figure"))return e.getStyle("width")}}}),e.for("downcast").attributeToAttribute({model:{name:"table",key:"tableWidth"},view:e=>({name:"figure",key:"style",value:{width:e}})}),e.elementToElement({model:"tableColumnGroup",view:"colgroup"}),e.elementToElement({model:"tableColumn",view:"col"}),e.for("downcast").add((e=>e.on("insert:table",((e,t,o)=>{const n=o.writer,i=t.item,l=o.mapper.toViewElement(i),r=l.is("element","table")?l:Array.from(l.getChildren()).find((e=>e.is("element","table")));te(i)?n.addClass("ck-table-resized",r):n.removeClass("ck-table-resized",r)}),{priority:"low"}))),e.for("upcast").add(Io(this._tableUtilsPlugin)),e.for("upcast").attributeToAttribute({view:{name:"col",styles:{width:/.*/}},model:{key:"columnWidth",value:e=>{const t=e.getStyle("width");return t&&(t.endsWith("%")||t.endsWith("pt"))?t:"auto"}}}),e.for("upcast").attributeToAttribute({view:{name:"col",key:"span"},model:"colSpan"}),e.for("downcast").attributeToAttribute({model:{name:"tableColumn",key:"columnWidth"},view:e=>({key:"style",value:{width:e}})})}_registerResizingListeners(){const e=this.editor.editing.view;e.addObserver(tt),e.document.on("mouseover",this._onMouseOverHandler.bind(this),{priority:"high"}),e.document.on("mousedown",this._onMouseDownHandler.bind(this),{priority:"high"}),e.document.on("mouseout",this._onMouseOutHandler.bind(this),{priority:"high"}),this._domEmitter.listenTo(i.global.window.document,"mousemove",function(e,t=0,o={}){"object"!=typeof o&&(o={});const{leading:n=!0,trailing:i=!0,signal:l}=o;return Mt(e,t,{leading:n,trailing:i,signal:l,maxWait:t})}(this._onMouseMoveHandler.bind(this),50)),this._domEmitter.listenTo(i.global.window.document,"mouseup",this._onMouseUpHandler.bind(this))}_recalculateResizerElement(e){const t=this.editor,o=t.editing.view.domConverter,n=o.mapViewToDom(e.findAncestor("table")),l=o.mapViewToDom(e.findAncestor((e=>["td","th"].includes(e.name)))),r=new i.Rect(n),a=new i.Rect(l),s=Po(Number((r.top-a.top).toFixed(4))),c=Po(Number((a.bottom-r.bottom).toFixed(4)));t.editing.view.change((t=>{t.setStyle("top",s,e),t.setStyle("bottom",c,e)}))}_resetResizerStyles(e){this.editor.editing.view.change((t=>{t.removeStyle("top",e),t.removeStyle("bottom",e)}))}_onMouseOverHandler(e,t){const o=t.target;o.hasClass("ck-table-column-resizer")&&this._isResizingAllowed&&this._recalculateResizerElement(o)}_onMouseOutHandler(e,t){const o=t.target;o.hasClass("ck-table-column-resizer")&&this._isResizingAllowed&&(this._isResizingActive||this._resetResizerStyles(o))}_onMouseDownHandler(e,t){const o=t.target;if(!o.hasClass("ck-table-column-resizer"))return;if(!this._isResizingAllowed)return;const n=this.editor,i=n.editing.mapper.toModelElement(o.findAncestor("figure"));n.model.canEditAt(i)&&(t.preventDefault(),e.stop(),this._initialMouseEventData=t)}_startResizingAfterThreshold(){const e=this._initialMouseEventData,{target:t}=e,o=this.editor.editing.mapper.toModelElement(t.findAncestor("figure")),n=t.findAncestor("table"),i=function(e,t,o){const n=Array(t.getColumns(e)),i=new _(e);for(const e of i){const t=o.editing.mapper.toViewElement(e.cell),i=Z(o.editing.view.domConverter.mapViewToDom(t));(!n[e.column]||i<n[e.column])&&(n[e.column]=X(i))}return n}(o,this._tableUtilsPlugin,this.editor);Array.from(n.getChildren()).find((e=>e.is("element","colgroup")))||this.editor.editing.view.change((e=>{!function(e,t,o){const n=e.createContainerElement("colgroup");for(let o=0;o<t.length;o++){const i=e.createEmptyElement("col"),l=`${X(t[o]/Y(t)*100)}%`;e.setStyle("width",l,i),e.insert(e.createPositionAt(n,"end"),i)}e.insert(e.createPositionAt(o,0),n)}(e,i,n)})),this._isResizingActive=!0,this._resizingData=this._getResizingData(e,i),this.editor.editing.view.change((e=>function(e,t,o){const n=o.widths.viewFigureWidth/o.widths.viewFigureParentWidth;e.addClass("ck-table-resized",t),e.addClass("ck-table-column-resizer__active",o.elements.viewResizer),e.setStyle("width",`${X(100*n)}%`,t.findAncestor("figure"))}(e,n,this._resizingData)))}_onMouseMoveHandler(e,t){if(this._initialMouseEventData){const e=this._initialMouseEventData.domEvent;if(!(Math.abs(t.clientX-e.clientX)>=3))return;this._startResizingAfterThreshold(),this._initialMouseEventData=null}if(!this._isResizingActive)return;if(!this._isResizingAllowed)return void this._onMouseUpHandler();const{columnPosition:o,flags:{isRightEdge:n,isTableCentered:i,isLtrContent:l},elements:{viewFigure:r,viewLeftColumn:a,viewRightColumn:s,viewResizer:c},widths:{viewFigureParentWidth:d,tableWidth:u,leftColumnWidth:b,rightColumnWidth:h}}=this._resizingData,m=40-b,g=n?d-u:h-40,p=(l?1:-1)*(n&&i?2:1),f=(w=(t.clientX-o)*p,_=Math.min(m,0),k=Math.max(g,0),X(w<=_?_:w>=k?k:w));var w,_,k;0!==f&&(this.editor.editing.view.change((e=>{const t=X(100*(b+f)/u);if(e.setStyle("width",`${t}%`,a),n){const t=X(100*(u+f)/d);e.setStyle("width",`${t}%`,r)}else{const t=X(100*(h-f)/u);e.setStyle("width",`${t}%`,s)}})),this._recalculateResizerElement(c))}_onMouseUpHandler(){if(this._initialMouseEventData=null,!this._isResizingActive)return;const{viewResizer:e,modelTable:t,viewFigure:o,viewColgroup:n}=this._resizingData.elements,i=this.editor,l=i.editing.view,r=this.getColumnGroupElement(t),a=Array.from(n.getChildren()).filter((e=>e.is("view:element"))),s=r?this.getTableColumnsWidths(r):null,c=a.map((e=>e.getStyle("width"))),d=!Vo(s,c),u=t.getAttribute("tableWidth"),b=o.getStyle("width"),h=u!==b;(d||h)&&(this._isResizingAllowed?i.execute("resizeTableWidth",{table:t,tableWidth:`${X(b)}%`,columnWidths:c}):l.change((e=>{if(s)for(const t of a)e.setStyle("width",s.shift(),t);else e.remove(n);h&&(u?e.setStyle("width",u,o):e.removeStyle("width",o)),s||u||e.removeClass("ck-table-resized",[...o.getChildren()].find((e=>"table"===e.name)))}))),l.change((t=>{t.removeClass("ck-table-column-resizer__active",e)}));l.domConverter.mapViewToDom(e).matches(":hover")||this._resetResizerStyles(e),this._isResizingActive=!1,this._resizingData=null}_getResizingData(e,t){const o=this.editor,n=e.domEvent.clientX,i=e.target,l=i.findAncestor("td")||i.findAncestor("th"),r=o.editing.mapper.toModelElement(l),a=r.findAncestor("table"),s=function(e,t){const o=t.getCellLocation(e).column;return{leftEdge:o,rightEdge:o+(e.getAttribute("colspan")||1)-1}}(r,this._tableUtilsPlugin).rightEdge,c=s===this._tableUtilsPlugin.getColumns(a)-1,d=!a.hasAttribute("tableAlignment"),u="rtl"!==o.locale.contentLanguageDirection,b=l.findAncestor("table"),h=b.findAncestor("figure"),m=[...b.getChildren()].find((e=>e.is("element","colgroup"))),g=m.getChild(s),p=c?void 0:m.getChild(s+1);return{columnPosition:n,flags:{isRightEdge:c,isTableCentered:d,isLtrContent:u},elements:{viewResizer:i,modelTable:a,viewFigure:h,viewColgroup:m,viewLeftColumn:g,viewRightColumn:p},widths:{viewFigureParentWidth:J(o.editing.view.domConverter.mapViewToDom(h.parent)),viewFigureWidth:J(o.editing.view.domConverter.mapViewToDom(h)),tableWidth:q(a,o),leftColumnWidth:t[s],rightColumnWidth:c?void 0:t[s+1]}}}_registerResizerInserter(){this.editor.conversion.for("editingDowncast").add((e=>{e.on("insert:tableCell",((e,t,o)=>{const n=t.item,i=o.mapper.toViewElement(n),l=o.writer;l.insert(l.createPositionAt(i,"end"),l.createUIElement("div",{class:"ck-table-column-resizer"}))}),{priority:"lowest"})}))}}var Lo=o(363),Bo={attributes:{"data-cke":!0}};Bo.setAttributes=Pe(),Bo.insert=Re().bind(null,"head"),Bo.domAPI=Se(),Bo.insertStyleElement=Le();Te()(Lo.A,Bo);Lo.A&&Lo.A.locals&&Lo.A.locals;class Do extends e.Plugin{static get requires(){return[Eo,oo]}static get pluginName(){return"TableColumnResize"}static get isOfficialPlugin(){return!0}}class Wo extends e.Command{refresh(){const e=g(this.editor.model.document.selection);e?(this.isEnabled=!0,this.value=e.getAttribute("tableType")):(this.isEnabled=!1,this.value=null)}execute(e){const t=this.editor.model,o=g(t.document.selection);o.getAttribute("tableType")!==e&&t.change((n=>{n.setAttribute("tableType",e,o),t.schema.removeDisallowedAttributes([o],n);const i=o.getChildren();for(const e of i)t.schema.checkChild(o,e)||n.remove(e)}))}}var zo=o(103),Fo={attributes:{"data-cke":!0}};Fo.setAttributes=Pe(),Fo.insert=Re().bind(null,"head"),Fo.domAPI=Se(),Fo.insertStyleElement=Le();Te()(zo.A,Fo);zo.A&&zo.A.locals&&zo.A.locals;const Oo=["content","layout"];class No extends e.Plugin{static get pluginName(){return"TableLayoutEditing"}static get requires(){return[Do]}static get isOfficialPlugin(){return!0}init(){this._defineSchema(),this._defineConverters(),this._defineClipboardPasteHandlers(),this._registerTableTypeAttributePostfixer(),this.editor.commands.add("insertTableLayout",new _o(this.editor)),this.editor.commands.add("tableType",new Wo(this.editor))}_defineSchema(){const{schema:e}=this.editor.model;e.extend("table",{allowAttributes:"tableType"}),e.addChildCheck(Ho,"caption"),e.addAttributeCheck(Ho,"headingRows"),e.addAttributeCheck(Ho,"headingColumns")}_defineConverters(){const{editor:e}=this,{conversion:t}=e,o=e.config.get("table.tableLayout.preferredExternalTableType");t.for("upcast").add(function(e){return t=>{t.on("element:table",((t,o,n)=>{const i=o.viewItem;if(!n.consumable.test(i,{name:!0}))return;if("content"==Mo(i,e))return;const l=n.writer.createElement("table",{tableType:"layout"});if(n.safeInsert(l,o.modelCursor)){n.consumable.consume(i,{name:!0}),n.consumable.consume(i,{attributes:["role"]}),n.consumable.consume(i,{classes:["layout-table"]});for(const e of i.getChildren())if(e.is("element"))for(const t of e.getChildren())t.is("element","tr")&&n.convertItem(t,n.writer.createPositionAt(l,"end"));if(n.convertChildren(i,n.writer.createPositionAt(l,"end")),l.isEmpty){const e=n.writer.createElement("tableRow");n.writer.insert(e,n.writer.createPositionAt(l,"end")),b(n.writer,n.writer.createPositionAt(e,"end"))}n.updateConversionResult(l,o)}}),{priority:"high"}),t.on("element:table",((t,o,n)=>{const{viewItem:i,modelRange:l}=o;l&&(n.writer.setAttribute("tableType",Mo(i,e),l),n.consumable.consume(i,{classes:["layout-table"]}),n.consumable.consume(i,{classes:["content-table"]}))}),{priority:"low"})}}(o)),t.for("dataDowncast").add((e=>e.on("attribute:tableType:table",((e,t,o)=>{const{item:n,attributeNewValue:i}=t,{mapper:l,writer:r}=o;if(!o.consumable.test(n,e.name))return;const a=l.toViewElement(n);r.addClass(`${i}-table`,a),"layout"==i&&r.setAttribute("role","presentation",a),o.consumable.consume(n,e.name)})))),t.for("editingDowncast").attributeToAttribute({model:{key:"tableType",values:["layout","content"]},view:{layout:{key:"class",value:["layout-table"]},content:{key:"class",value:["content-table"]}}})}_defineClipboardPasteHandlers(){const{plugins:e}=this.editor;if(!e.has("ClipboardPipeline"))return;const t=e.get("ClipboardPipeline");this.listenTo(t,"contentInsertion",((e,t)=>{t.sourceEditorId||this.editor.model.change((e=>{for(const{item:o}of e.createRangeIn(t.content))o.is("element","table")&&e.setAttribute("tableType","content",o)}))}))}_registerTableTypeAttributePostfixer(){const e=this.editor;e.model.document.registerPostFixer((t=>{const o=e.model.document.differ.getChanges();let n=!1;for(const i of o){if("insert"==i.type&&"$text"!=i.name){const e=i.position.nodeAfter,o=t.createRangeOn(e);for(const e of o.getItems())e.is("element","table")&&!e.hasAttribute("tableType")&&(t.setAttribute("tableType","content",e),n=!0)}if("attribute"==i.type&&"tableType"==i.attributeKey)for(const o of i.range.getItems())if(o.is("element","table")){e.model.schema.removeDisallowedAttributes([o],t);const i=o.getChildren();for(const l of i)e.model.schema.checkChild(o,l)||(t.remove(l),n=!0)}}return n}))}}function Mo(e,t){if(e.hasClass("content-table"))return"content";if(e.hasClass("layout-table"))return"layout";if(t&&Oo.includes(t))return t;return e.parent.is("element","figure")||Array.from(e.getChildren()).some((e=>e.is("element","caption")))?"content":"layout"}function Ho(e){if(e.endsWith("table")&&"layout"==e.last.getAttribute("tableType"))return!1}class jo extends e.Plugin{static get pluginName(){return"TableLayout"}static get isOfficialPlugin(){return!0}static get requires(){return[at,Do,No,po]}}class Uo extends e.Command{attributeName;_defaultValue;_defaultContentTableValue;_defaultLayoutTableValue;constructor(e,t,o){super(e),this.attributeName=t,this._defaultContentTableValue=o,this._defaultLayoutTableValue="tableBorderStyle"===t?"none":void 0}refresh(){const e=g(this.editor.model.document.selection);this._defaultValue=e&&"layout"===e.getAttribute("tableType")?this._defaultLayoutTableValue:this._defaultContentTableValue,this.isEnabled=!!e,this.value=this._getValue(e)}execute(e={}){const t=this.editor.model,o=t.document.selection,{value:n,batch:i}=e,l=g(o),r=this._getValueToSet(n);t.enqueueChange(i,(e=>{r?e.setAttribute(this.attributeName,r,l):e.removeAttribute(this.attributeName,l)}))}_getValue(e){if(!e)return;const t=e.getAttribute(this.attributeName);return t!==this._defaultValue?t:void 0}_getValueToSet(e){if(e!==this._defaultValue)return e}}class $o extends Uo{constructor(e,t){super(e,"tableBackgroundColor",t)}}class Ko extends Uo{constructor(e,t){super(e,"tableBorderColor",t)}_getValue(e){if(!e)return;const t=qt(e.getAttribute(this.attributeName));return t!==this._defaultValue?t:void 0}}class qo extends Uo{constructor(e,t){super(e,"tableBorderStyle",t)}_getValue(e){if(!e)return;const t=qt(e.getAttribute(this.attributeName));return t!==this._defaultValue?t:void 0}}class Go extends Uo{constructor(e,t){super(e,"tableBorderWidth",t)}_getValue(e){if(!e)return;const t=qt(e.getAttribute(this.attributeName));return t!==this._defaultValue?t:void 0}_getValueToSet(e){const t=Gt(e,"px");if(t!==this._defaultValue)return t}}class Jo extends Uo{constructor(e,t){super(e,"tableWidth",t)}_getValueToSet(e){if((e=Gt(e,"px"))!==this._defaultValue)return e}}class Xo extends Uo{constructor(e,t){super(e,"tableHeight",t)}_getValueToSet(e){if((e=Gt(e,"px"))!==this._defaultValue)return e}}class Yo extends Uo{constructor(e,t){super(e,"tableAlignment",t)}}const Qo=/^(left|center|right)$/,Zo=/^(left|none|right)$/;class en extends e.Plugin{static get pluginName(){return"TablePropertiesEditing"}static get isOfficialPlugin(){return!0}static get requires(){return[We]}init(){const e=this.editor,t=e.model.schema,o=e.conversion;e.config.define("table.tableProperties.defaultProperties",{});const n=Xt(e.config.get("table.tableProperties.defaultProperties"),{includeAlignmentProperty:!0});e.data.addStyleProcessorRules(et.addBorderRules),function(e,t,o){const n={width:"tableBorderWidth",color:"tableBorderColor",style:"tableBorderStyle"};e.extend("table",{allowAttributes:Object.values(n)}),r(t,"table",n,o),s(t,{modelAttribute:n.color,styleName:"border-color"}),s(t,{modelAttribute:n.style,styleName:"border-style"}),s(t,{modelAttribute:n.width,styleName:"border-width"})}(t,o,{color:n.borderColor,style:n.borderStyle,width:n.borderWidth}),e.commands.add("tableBorderColor",new Ko(e,n.borderColor)),e.commands.add("tableBorderStyle",new qo(e,n.borderStyle)),e.commands.add("tableBorderWidth",new Go(e,n.borderWidth)),function(e,t,o){e.extend("table",{allowAttributes:["tableAlignment"]}),t.for("downcast").attributeToAttribute({model:{name:"table",key:"tableAlignment",values:["left","center","right"]},view:{left:{key:"style",value:{float:"left"}},right:{key:"style",value:{float:"right"}},center:(e,t,o)=>({key:"style",value:"layout"!==o.item.getAttribute("tableType")?{float:"none"}:{"margin-left":"auto","margin-right":"auto"}})},converterPriority:"high"}),t.for("upcast").attributeToAttribute({view:{name:/^(table|figure)$/,styles:{float:Zo}},model:{key:"tableAlignment",value:(e,t,n)=>{const i=c(o,"",n);let l=e.getStyle("float");return"none"===l&&(l="center"),l===i?null:l}}}).attributeToAttribute({view:{name:/^(table|figure)$/,styles:{"margin-left":"auto","margin-right":"auto"}},model:{key:"tableAlignment",value:(e,t,n)=>{const i="center";return i===c(o,"",n)?null:i}}}).attributeToAttribute({view:{attributes:{align:Qo}},model:{name:"table",key:"tableAlignment",value:(e,t,n)=>{const i=c(o,"",n),l=e.getAttribute("align");return l===i?null:l}}})}(t,o,n.alignment),e.commands.add("tableAlignment",new Yo(e,n.alignment)),tn(t,o,{modelAttribute:"tableWidth",styleName:"width",defaultValue:n.width}),e.commands.add("tableWidth",new Jo(e,n.width)),tn(t,o,{modelAttribute:"tableHeight",styleName:"height",defaultValue:n.height}),e.commands.add("tableHeight",new Xo(e,n.height)),e.data.addStyleProcessorRules(et.addBackgroundRules),function(e,t,o){const{modelAttribute:n}=o;e.extend("table",{allowAttributes:[n]}),l(t,{viewElement:"table",...o}),s(t,o)}(t,o,{modelAttribute:"tableBackgroundColor",styleName:"background-color",defaultValue:n.backgroundColor}),e.commands.add("tableBackgroundColor",new $o(e,n.backgroundColor))}}function tn(e,t,o){const{modelAttribute:n}=o;e.extend("table",{allowAttributes:[n]}),l(t,{viewElement:/^(table|figure)$/,shouldUpcast:e=>!("table"==e.name&&"figure"==e.parent.name),...o}),a(t,{modelElement:"table",...o})}var on=o(218),nn={attributes:{"data-cke":!0}};nn.setAttributes=Pe(),nn.insert=Re().bind(null,"head"),nn.domAPI=Se(),nn.insertStyleElement=Le();Te()(on.A,nn);on.A&&on.A.locals&&on.A.locals;class ln extends Oe.View{options;focusTracker;keystrokes;children;borderStyleDropdown;borderWidthInput;borderColorInput;backgroundInput;widthInput;heightInput;alignmentToolbar;saveButtonView;cancelButtonView;_focusables;_focusCycler;constructor(e,t){super(e),this.set({borderStyle:"",borderWidth:"",borderColor:"",backgroundColor:"",width:"",height:"",alignment:""}),this.options=t;const{borderStyleDropdown:o,borderWidthInput:n,borderColorInput:l,borderRowLabel:r}=this._createBorderFields(),{backgroundRowLabel:a,backgroundInput:s}=this._createBackgroundFields(),{widthInput:c,operatorLabel:d,heightInput:u,dimensionsLabel:b}=this._createDimensionFields(),{alignmentToolbar:h,alignmentLabel:m}=this._createAlignmentFields();this.focusTracker=new i.FocusTracker,this.keystrokes=new i.KeystrokeHandler,this.children=this.createCollection(),this.borderStyleDropdown=o,this.borderWidthInput=n,this.borderColorInput=l,this.backgroundInput=s,this.widthInput=c,this.heightInput=u,this.alignmentToolbar=h;const{saveButtonView:g,cancelButtonView:p}=this._createActionButtons();this.saveButtonView=g,this.cancelButtonView=p,this._focusables=new Oe.ViewCollection,this._focusCycler=new Oe.FocusCycler({focusables:this._focusables,focusTracker:this.focusTracker,keystrokeHandler:this.keystrokes,actions:{focusPrevious:"shift + tab",focusNext:"tab"}}),this.children.add(new Oe.FormHeaderView(e,{label:this.t("Table properties")})),this.children.add(new Oe.FormRowView(e,{labelView:r,children:[r,o,l,n],class:"ck-table-form__border-row"})),this.children.add(new Oe.FormRowView(e,{labelView:a,children:[a,s],class:"ck-table-form__background-row"})),this.children.add(new Oe.FormRowView(e,{children:[new Oe.FormRowView(e,{labelView:b,children:[b,c,d,u],class:"ck-table-form__dimensions-row"}),new Oe.FormRowView(e,{labelView:m,children:[m,h],class:"ck-table-properties-form__alignment-row"})]})),this.children.add(new Oe.FormRowView(e,{children:[this.saveButtonView,this.cancelButtonView],class:"ck-table-form__action-row"})),this.setTemplate({tag:"form",attributes:{class:["ck","ck-form","ck-table-form","ck-table-properties-form"],tabindex:"-1"},children:this.children})}render(){super.render(),(0,Oe.submitHandler)({view:this}),[this.borderColorInput,this.backgroundInput].forEach((e=>{this._focusCycler.chain(e.fieldView.focusCycler)})),[this.borderStyleDropdown,this.borderColorInput,this.borderWidthInput,this.backgroundInput,this.widthInput,this.heightInput,this.alignmentToolbar,this.saveButtonView,this.cancelButtonView].forEach((e=>{this._focusables.add(e),this.focusTracker.add(e.element)})),this.keystrokes.listenTo(this.element)}destroy(){super.destroy(),this.focusTracker.destroy(),this.keystrokes.destroy()}focus(){this._focusCycler.focusFirst()}_createBorderFields(){const e=this.options.defaultTableProperties,t={style:e.borderStyle,width:e.borderWidth,color:e.borderColor},o=Vt({colorConfig:this.options.borderColors,columns:5,defaultColorValue:t.color,colorPickerConfig:this.options.colorPickerConfig}),n=this.locale,i=this.t,l=i("Style"),r=new Oe.LabelView(n);r.text=i("Border");const a=_t(i),s=new Oe.LabeledFieldView(n,Oe.createLabeledDropdown);s.set({label:l,class:"ck-table-form__border-style"}),s.fieldView.buttonView.set({ariaLabel:l,ariaLabelledBy:void 0,isOn:!1,withText:!0,tooltip:l}),s.fieldView.buttonView.bind("label").to(this,"borderStyle",(e=>a[e||"none"])),s.fieldView.on("execute",(e=>{this.borderStyle=e.source._borderStyleValue})),s.bind("isEmpty").to(this,"borderStyle",(e=>!e)),(0,Oe.addListToDropdown)(s.fieldView,Tt(this,t.style),{role:"menu",ariaLabel:l});const c=new Oe.LabeledFieldView(n,Oe.createLabeledInputText);c.set({label:i("Width"),class:"ck-table-form__border-width"}),c.fieldView.bind("value").to(this,"borderWidth"),c.bind("isEnabled").to(this,"borderStyle",rn),c.fieldView.on("input",(()=>{this.borderWidth=c.fieldView.element.value}));const d=new Oe.LabeledFieldView(n,o);return d.set({label:i("Color"),class:"ck-table-form__border-color"}),d.fieldView.bind("value").to(this,"borderColor"),d.bind("isEnabled").to(this,"borderStyle",rn),d.fieldView.on("input",(()=>{this.borderColor=d.fieldView.value})),this.on("change:borderStyle",((e,o,n,i)=>{rn(n)||(this.borderColor="",this.borderWidth=""),rn(i)||(this.borderColor=t.color,this.borderWidth=t.width)})),{borderRowLabel:r,borderStyleDropdown:s,borderColorInput:d,borderWidthInput:c}}_createBackgroundFields(){const e=this.locale,t=this.t,o=new Oe.LabelView(e);o.text=t("Background");const n=Vt({colorConfig:this.options.backgroundColors,columns:5,defaultColorValue:this.options.defaultTableProperties.backgroundColor,colorPickerConfig:this.options.colorPickerConfig}),i=new Oe.LabeledFieldView(e,n);return i.set({label:t("Color"),class:"ck-table-properties-form__background"}),i.fieldView.bind("value").to(this,"backgroundColor"),i.fieldView.on("input",(()=>{this.backgroundColor=i.fieldView.value})),{backgroundRowLabel:o,backgroundInput:i}}_createDimensionFields(){const e=this.locale,t=this.t,o=new Oe.LabelView(e);o.text=t("Dimensions");const n=new Oe.LabeledFieldView(e,Oe.createLabeledInputText);n.set({label:t("Width"),class:"ck-table-form__dimensions-row__width"}),n.fieldView.bind("value").to(this,"width"),n.fieldView.on("input",(()=>{this.width=n.fieldView.element.value}));const i=new Oe.View(e);i.setTemplate({tag:"span",attributes:{class:["ck-table-form__dimension-operator"]},children:[{text:"×"}]});const l=new Oe.LabeledFieldView(e,Oe.createLabeledInputText);return l.set({label:t("Height"),class:"ck-table-form__dimensions-row__height"}),l.fieldView.bind("value").to(this,"height"),l.fieldView.on("input",(()=>{this.height=l.fieldView.element.value})),{dimensionsLabel:o,widthInput:n,operatorLabel:i,heightInput:l}}_createAlignmentFields(){const e=this.locale,t=this.t,o=new Oe.LabelView(e);o.text=t("Alignment");const n=new Oe.ToolbarView(e);return n.set({role:"radiogroup",isCompact:!0,ariaLabel:t("Table alignment toolbar")}),xt({view:this,icons:{left:Fe.IconObjectInlineLeft,center:Fe.IconObjectCenter,right:Fe.IconObjectInlineRight},toolbar:n,labels:this._alignmentLabels,propertyName:"alignment",defaultValue:this.options.defaultTableProperties.alignment}),{alignmentLabel:o,alignmentToolbar:n}}_createActionButtons(){const e=this.locale,t=this.t,o=new Oe.ButtonView(e),n=new Oe.ButtonView(e),i=[this.borderWidthInput,this.borderColorInput,this.backgroundInput,this.widthInput,this.heightInput];return o.set({label:t("Save"),icon:Fe.IconCheck,class:"ck-button-save",type:"submit",withText:!0}),o.bind("isEnabled").toMany(i,"errorText",((...e)=>e.every((e=>!e)))),n.set({label:t("Cancel"),icon:Fe.IconCancel,class:"ck-button-cancel",withText:!0}),n.delegate("execute").to(this,"cancel"),{saveButtonView:o,cancelButtonView:n}}get _alignmentLabels(){const e=this.locale,t=this.t,o=t("Align table to the left"),n=t("Center table"),i=t("Align table to the right");return"rtl"===e.uiLanguageDirection?{right:i,center:n,left:o}:{left:o,center:n,right:i}}}function rn(e){return"none"!==e}const an={borderStyle:"tableBorderStyle",borderColor:"tableBorderColor",borderWidth:"tableBorderWidth",backgroundColor:"tableBackgroundColor",width:"tableWidth",height:"tableHeight",alignment:"tableAlignment"};class sn extends e.Plugin{_defaultContentTableProperties;_defaultLayoutTableProperties;_balloon;view=null;_viewWithContentTableDefaults=null;_viewWithLayoutTableDefaults=null;_undoStepBatch;_isReady;static get requires(){return[Oe.ContextualBalloon]}static get pluginName(){return"TablePropertiesUI"}static get isOfficialPlugin(){return!0}constructor(e){super(e),e.config.define("table.tableProperties",{borderColors:St,backgroundColors:St})}init(){const e=this.editor;this._defaultContentTableProperties=Xt(e.config.get("table.tableProperties.defaultProperties"),{includeAlignmentProperty:!0}),this._defaultLayoutTableProperties=Jt(),this._balloon=e.plugins.get(Oe.ContextualBalloon),e.ui.componentFactory.add("tableProperties",(()=>this._createTablePropertiesButton()))}_createTablePropertiesButton(){const e=this.editor,t=e.t,o=new Oe.ButtonView(e.locale);o.set({label:t("Table properties"),icon:Fe.IconTableProperties,tooltip:!0}),this.listenTo(o,"execute",(()=>this._showView()));const n=Object.values(an).map((t=>e.commands.get(t)));return o.bind("isEnabled").toMany(n,"isEnabled",((...e)=>e.some((e=>e)))),o}destroy(){super.destroy(),this.view&&this.view.destroy()}_createPropertiesView(e){const t=this.editor,o=t.config.get("table.tableProperties"),n=(0,Oe.normalizeColorOptions)(o.borderColors),i=(0,Oe.getLocalizedColorOptions)(t.locale,n),l=(0,Oe.normalizeColorOptions)(o.backgroundColors),r=(0,Oe.getLocalizedColorOptions)(t.locale,l),a=!1!==o.colorPicker,s=new ln(t.locale,{borderColors:i,backgroundColors:r,defaultTableProperties:e,colorPickerConfig:!!a&&(o.colorPicker||{})}),c=t.t;s.render(),this.listenTo(s,"submit",(()=>{this._hideView()})),this.listenTo(s,"cancel",(()=>{this._undoStepBatch.operations.length&&t.execute("undo",this._undoStepBatch),this._hideView()})),s.keystrokes.set("Esc",((e,t)=>{this._hideView(),t()})),(0,Oe.clickOutsideHandler)({emitter:s,activator:()=>this._isViewInBalloon,contextElements:[this._balloon.view.element],callback:()=>this._hideView()});const d=kt(c),u=Ct(c);return s.on("change:borderStyle",this._getPropertyChangeCallback("tableBorderStyle")),s.on("change:borderColor",this._getValidatedPropertyChangeCallback({viewField:s.borderColorInput,commandName:"tableBorderColor",errorText:d,validator:yt})),s.on("change:borderWidth",this._getValidatedPropertyChangeCallback({viewField:s.borderWidthInput,commandName:"tableBorderWidth",errorText:u,validator:At})),s.on("change:backgroundColor",this._getValidatedPropertyChangeCallback({viewField:s.backgroundInput,commandName:"tableBackgroundColor",errorText:d,validator:yt})),s.on("change:width",this._getValidatedPropertyChangeCallback({viewField:s.widthInput,commandName:"tableWidth",errorText:u,validator:vt})),s.on("change:height",this._getValidatedPropertyChangeCallback({viewField:s.heightInput,commandName:"tableHeight",errorText:u,validator:vt})),s.on("change:alignment",this._getPropertyChangeCallback("tableAlignment")),s}_fillViewFormFromCommandValues(){const e=this.editor.commands,t=e.get("tableBorderStyle");Object.entries(an).map((([t,o])=>{const n=t,i=this.view===this._viewWithContentTableDefaults?this._defaultContentTableProperties[n]||"":this._defaultLayoutTableProperties[n]||"";return[n,e.get(o).value||i]})).forEach((([e,o])=>{("borderColor"!==e&&"borderWidth"!==e||"none"!==t.value)&&this.view.set(e,o)})),this._isReady=!0}_showView(){const e=this.editor,t=ct(e.editing.view.document.selection),o=t&&e.editing.mapper.toModelElement(t),n=!o||"layout"!==o.getAttribute("tableType");n&&!this._viewWithContentTableDefaults?this._viewWithContentTableDefaults=this._createPropertiesView(this._defaultContentTableProperties):n||this._viewWithLayoutTableDefaults||(this._viewWithLayoutTableDefaults=this._createPropertiesView(this._defaultLayoutTableProperties)),this.view=n?this._viewWithContentTableDefaults:this._viewWithLayoutTableDefaults,this.listenTo(e.ui,"update",(()=>{this._updateView()})),this._fillViewFormFromCommandValues(),this._balloon.add({view:this.view,position:Ut(e)}),this._undoStepBatch=e.model.createBatch(),this.view.focus()}_hideView(){const e=this.editor;this.stopListening(e.ui,"update"),this._isReady=!1,this.view.saveButtonView.focus(),this._balloon.remove(this.view),this.editor.editing.view.focus()}_updateView(){const e=this.editor;ct(e.editing.view.document.selection)?this._isViewVisible&&jt(e,"table"):this._hideView()}get _isViewVisible(){return!!this.view&&this._balloon.visibleView===this.view}get _isViewInBalloon(){return!!this.view&&this._balloon.hasView(this.view)}_getPropertyChangeCallback(e){return(t,o,n)=>{this._isReady&&this.editor.execute(e,{value:n,batch:this._undoStepBatch})}}_getValidatedPropertyChangeCallback(e){const{commandName:t,viewField:o,validator:n,errorText:i}=e,l=Mt((()=>{o.errorText=i}),500);return(e,i,r)=>{l.cancel(),this._isReady&&(n(r)?(this.editor.execute(t,{value:r,batch:this._undoStepBatch}),o.errorText=null):l())}}}class cn extends e.Plugin{static get pluginName(){return"TableProperties"}static get isOfficialPlugin(){return!0}static get requires(){return[en,sn]}}function dn(e){e.document.registerPostFixer((t=>function(e,t){const o=t.document.differ.getChanges();let n=!1;for(const t of o){if("insert"!=t.type)continue;const o=t.position.parent;if(o.is("element","table")||"table"==t.name){const i="table"==t.name?t.position.nodeAfter:o,l=Array.from(i.getChildren()).filter((e=>e.is("element","caption"))),r=l.shift();if(!r)continue;for(const t of l)e.move(e.createRangeIn(t),r,"end"),e.remove(t);r.nextSibling&&(e.move(e.createRangeOn(r),i,"end"),n=!0),n=!!l.length||n}}return n}(t,e)))}function un(e){return!!e&&e.is("element","table")}function bn(e){for(const t of e.getChildren())if(t.is("element","caption"))return t;return null}function hn(e){const t=e.parent;return"figcaption"==e.name&&t&&t.is("element","figure")&&t.hasClass("table")||"caption"==e.name&&t&&t.is("element","table")?{name:!0}:null}class mn extends e.Command{refresh(){const e=this.editor,t=g(e.model.document.selection);this.isEnabled=!!t&&e.model.schema.checkChild(t,"caption"),this.isEnabled?this.value=!!bn(t):this.value=!1}execute({focusCaptionOnShow:e=!1}={}){this.editor.model.change((t=>{this.value?this._hideTableCaption(t):this._showTableCaption(t,e)}))}_showTableCaption(e,t){const o=this.editor.model,n=g(o.document.selection),i=this.editor.plugins.get("TableCaptionEditing")._getSavedCaption(n)||e.createElement("caption");o.insertContent(i,n,"end"),t&&e.setSelection(i,"in")}_hideTableCaption(e){const t=this.editor.model,o=g(t.document.selection),n=this.editor.plugins.get("TableCaptionEditing"),i=bn(o);n._saveCaption(o,i),t.deleteContent(e.createSelection(i,"on"))}}class gn extends e.Plugin{_savedCaptionsMap;static get pluginName(){return"TableCaptionEditing"}static get isOfficialPlugin(){return!0}constructor(e){super(e),this._savedCaptionsMap=new WeakMap}init(){const e=this.editor,o=e.model.schema,n=e.editing.view,i=e.t;o.isRegistered("caption")?o.extend("caption",{allowIn:"table"}):o.register("caption",{allowIn:"table",allowContentOf:"$block",isLimit:!0}),e.commands.add("toggleTableCaption",new mn(this.editor)),e.conversion.for("upcast").elementToElement({view:hn,model:"caption"}),e.conversion.for("dataDowncast").elementToElement({model:"caption",view:(e,{writer:t})=>un(e.parent)?t.createContainerElement("figcaption"):null}),e.conversion.for("editingDowncast").elementToElement({model:"caption",view:(e,{writer:o})=>{if(!un(e.parent))return null;const l=o.createEditableElement("figcaption");return o.setCustomProperty("tableCaption",!0,l),l.placeholder=i("Enter table caption"),(0,et.enablePlaceholder)({view:n,element:l,keepOnFocus:!0}),(0,t.toWidgetEditable)(l,o)}}),dn(e.model)}_getSavedCaption(e){const t=this._savedCaptionsMap.get(e);return t?et.Element.fromJSON(t):null}_saveCaption(e,t){this._savedCaptionsMap.set(e,t.toJSON())}}class pn extends e.Plugin{static get pluginName(){return"TableCaptionUI"}static get isOfficialPlugin(){return!0}init(){const e=this.editor,t=e.editing.view,o=e.t;e.ui.componentFactory.add("toggleTableCaption",(n=>{const i=e.commands.get("toggleTableCaption"),l=new Oe.ButtonView(n);return l.set({icon:Fe.IconCaption,tooltip:!0,isToggleable:!0}),l.bind("isOn","isEnabled").to(i,"value","isEnabled"),l.bind("label").to(i,"value",(e=>o(e?"Toggle caption off":"Toggle caption on"))),this.listenTo(l,"execute",(()=>{if(e.execute("toggleTableCaption",{focusCaptionOnShow:!0}),i.value){const o=function(e){const t=g(e);return t?bn(t):null}(e.model.document.selection),n=e.editing.mapper.toViewElement(o);if(!n)return;t.scrollToTheSelection(),t.change((e=>{e.addClass("table__caption_highlighted",n)}))}e.editing.view.focus()})),l}))}}var fn=o(175),wn={attributes:{"data-cke":!0}};wn.setAttributes=Pe(),wn.insert=Re().bind(null,"head"),wn.domAPI=Se(),wn.insertStyleElement=Le();Te()(fn.A,wn);fn.A&&fn.A.locals&&fn.A.locals;class _n extends e.Plugin{static get pluginName(){return"TableCaption"}static get isOfficialPlugin(){return!0}static get requires(){return[gn,pn]}}})(),(window.CKEditor5=window.CKEditor5||{}).table=n})();