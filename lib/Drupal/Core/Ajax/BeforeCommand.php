<?php

namespace <PERSON><PERSON>al\Core\Ajax;

/**
 * An AJAX command for calling the jQuery before() method.
 *
 * The 'insert/before' command instructs the client to use jQ<PERSON>y's before()
 * method to insert the given render array or HTML content before each of
 * elements matched by the given selector.
 *
 * This command is implemented by Drupal.AjaxCommands.prototype.insert()
 * defined in misc/ajax.js.
 *
 * @see http://docs.jquery.com/Manipulation/before#content
 *
 * @ingroup ajax
 */
class BeforeCommand extends InsertCommand {

  /**
   * Implements Drupal\Core\Ajax\CommandInterface:render().
   */
  public function render() {

    return [
      'command' => 'insert',
      'method' => 'before',
      'selector' => $this->selector,
      'data' => $this->getRenderedContent(),
      'settings' => $this->settings,
    ];
  }

}
