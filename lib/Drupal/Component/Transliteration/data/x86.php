<?php

/**
 * @file
 * Generic transliteration data for the PhpTransliteration class.
 */

$base = [
  0x00 => 'tuo', 'wu', 'rui', 'rui', 'qi', 'heng', 'lu', 'su', 'tui', 'meng', 'yun', 'ping', 'yu', 'xun', 'ji', 'jiong',
  0x10 => 'xuan', 'mo', 'qiu', 'su', 'jiong', 'feng', 'nie', 'bo', 'rang', 'yi', 'xian', 'yu', 'ju', 'lian', 'lian', 'yin',
  0x20 => 'qiang', 'ying', 'long', 'tou', 'wei', 'yue', 'ling', 'qu', 'yao', 'fan', 'mei', 'han', 'kui', 'lan', 'ji', 'dang',
  0x30 => 'man', 'lei', 'lei', 'hui', 'feng', 'zhi', 'wei', 'kui', 'zhan', 'huai', 'li', 'ji', 'mi', 'lei', 'huai', 'luo',
  0x40 => 'ji', 'kui', 'lu', 'jian', 'sa', 'teng', 'lei', 'quan', 'xiao', 'yi', 'luan', 'men', 'bie', 'hu', 'hu', 'lu',
  0x50 => 'nue', 'lu', 'si', 'xiao', 'qian', 'chu', 'hu', 'xu', 'cuo', 'fu', 'xu', 'xu', 'lu', 'hu', 'yu', 'hao',
  0x60 => 'jiao', 'ju', 'guo', 'bao', 'yan', 'zhan', 'zhan', 'kui', 'bin', 'xi', 'shu', 'chong', 'qiu', 'diao', 'ji', 'qiu',
  0x70 => 'ding', 'shi', 'xia', 'jue', 'zhe', 'she', 'yu', 'han', 'zi', 'hong', 'hui', 'meng', 'ge', 'sui', 'xia', 'chai',
  0x80 => 'shi', 'yi', 'ma', 'xiang', 'fang', 'e', 'ba', 'chi', 'qian', 'wen', 'wen', 'rui', 'bang', 'pi', 'yue', 'yue',
  0x90 => 'jun', 'qi', 'tong', 'yin', 'qi', 'can', 'yuan', 'jue', 'hui', 'qin', 'qi', 'zhong', 'ya', 'hao', 'mu', 'wang',
  0xA0 => 'fen', 'fen', 'hang', 'gong', 'zao', 'fu', 'ran', 'jie', 'fu', 'chi', 'dou', 'bao', 'xian', 'ni', 'te', 'qiu',
  0xB0 => 'you', 'zha', 'ping', 'chi', 'you', 'he', 'han', 'ju', 'li', 'fu', 'ran', 'zha', 'gou', 'pi', 'pi', 'xian',
  0xC0 => 'zhu', 'diao', 'bie', 'bing', 'gu', 'zhan', 'qu', 'she', 'tie', 'ling', 'gu', 'dan', 'gu', 'ying', 'li', 'cheng',
  0xD0 => 'qu', 'mou', 'ge', 'ci', 'hui', 'hui', 'mang', 'fu', 'yang', 'wa', 'lie', 'zhu', 'yi', 'xian', 'kuo', 'jiao',
  0xE0 => 'li', 'yi', 'ping', 'qi', 'ha', 'she', 'yi', 'wang', 'mo', 'qiong', 'qie', 'gui', 'qiong', 'zhi', 'man', 'lao',
  0xF0 => 'zhe', 'jia', 'nao', 'si', 'qi', 'xing', 'jie', 'qiu', 'shao', 'yong', 'jia', 'tui', 'che', 'bai', 'e', 'han',
];
