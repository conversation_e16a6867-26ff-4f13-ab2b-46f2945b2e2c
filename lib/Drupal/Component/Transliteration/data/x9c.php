<?php

/**
 * @file
 * Generic transliteration data for the PhpTransliteration class.
 */

$base = [
  0x00 => 'huan', 'quan', 'zei', 'wei', 'wei', 'yu', 'chun', 'rou', 'die', 'huang', 'lian', 'yan', 'qiu', 'qiu', 'jian', 'bi',
  0x10 => 'e', 'yang', 'fu', 'sai', 'gan', 'xia', 'tuo', 'hu', 'shi', 'ruo', 'xuan', 'wen', 'qian', 'hao', 'wu', 'fang',
  0x20 => 'sao', 'liu', 'ma', 'shi', 'shi', 'guan', 'zi', 'teng', 'ta', 'yao', 'e', 'yong', 'qian', 'qi', 'wen', 'ruo',
  0x30 => 'shen', 'lian', 'ao', 'le', 'hui', 'min', 'ji', 'tiao', 'qu', 'jian', 'shen', 'man', 'xi', 'qiu', 'biao', 'ji',
  0x40 => 'ji', 'zhu', 'jiang', 'xiu', 'zhuan', 'yong', 'zhang', 'kang', 'xue', 'bie', 'yu', 'qu', 'xiang', 'bo', 'jiao', 'xun',
  0x50 => 'su', 'huang', 'zun', 'shan', 'shan', 'fan', 'gui', 'lin', 'xun', 'miao', 'xi', 'zeng', 'xiang', 'fen', 'guan', 'hou',
  0x60 => 'kuai', 'zei', 'sao', 'zhan', 'gan', 'gui', 'ying', 'li', 'chang', 'lei', 'shu', 'ai', 'ru', 'ji', 'xu', 'hu',
  0x70 => 'shu', 'li', 'lie', 'li', 'mie', 'zhen', 'xiang', 'e', 'lu', 'guan', 'li', 'xian', 'yu', 'dao', 'ji', 'you',
  0x80 => 'tun', 'lu', 'fang', 'ba', 'he', 'ba', 'ping', 'nian', 'lu', 'you', 'zha', 'fu', 'ba', 'bao', 'hou', 'pi',
  0x90 => 'tai', 'gui', 'jie', 'kao', 'wei', 'er', 'tong', 'zei', 'hou', 'kuai', 'ji', 'jiao', 'xian', 'zha', 'xiang', 'xun',
  0xA0 => 'geng', 'li', 'lian', 'jian', 'li', 'shi', 'tiao', 'gun', 'sha', 'huan', 'jun', 'ji', 'yong', 'qing', 'ling', 'qi',
  0xB0 => 'zou', 'fei', 'kun', 'chang', 'gu', 'ni', 'nian', 'diao', 'jing', 'shen', 'shi', 'zi', 'fen', 'die', 'bi', 'chang',
  0xC0 => 'ti', 'wen', 'wei', 'sai', 'e', 'qiu', 'fu', 'huang', 'quan', 'jiang', 'bian', 'sao', 'ao', 'qi', 'ta', 'guan',
  0xD0 => 'yao', 'pang', 'jian', 'le', 'biao', 'xue', 'bie', 'man', 'min', 'yong', 'wei', 'xi', 'gui', 'shan', 'lin', 'zun',
  0xE0 => 'hu', 'gan', 'li', 'zhan', 'guan', 'niao', 'yi', 'fu', 'li', 'jiu', 'bu', 'yan', 'fu', 'diao', 'ji', 'feng',
  0xF0 => 'ru', 'gan', 'shi', 'feng', 'ming', 'bao', 'yuan', 'zhi', 'hu', 'qin', 'fu', 'ban', 'wen', 'jian', 'shi', 'yu',
];
