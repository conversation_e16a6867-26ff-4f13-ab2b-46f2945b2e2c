<?php

/**
 * @file
 * Generic transliteration data for the PhpTransliteration class.
 */

$base = [
  0x00 => 'ba', 'fang', 'chen', 'xing', 'dou', 'yue', 'qian', 'fu', 'pi', 'na', 'xin', 'e', 'jue', 'dun', 'gou', 'yin',
  0x10 => 'qian', 'ban', 'sa', 'ren', 'chao', 'niu', 'fen', 'yun', 'ji', 'qin', 'pi', 'guo', 'hong', 'yin', 'jun', 'shi',
  0x20 => 'yi', 'zhong', 'xi', 'gai', 'ri', 'huo', 'tai', 'kang', 'yuan', 'lu', 'e', 'wen', 'duo', 'zi', 'ni', 'tu',
  0x30 => 'shi', 'min', 'gu', 'ke', 'ling', 'bing', 'si', 'gu', 'bo', 'pi', 'yu', 'si', 'zuo', 'bu', 'you', 'tian',
  0x40 => 'jia', 'zhen', 'shi', 'shi', 'zhi', 'ju', 'chan', 'shi', 'shi', 'xuan', 'zhao', 'bao', 'he', 'bi', 'sheng', 'chu',
  0x50 => 'shi', 'bo', 'zhu', 'chi', 'za', 'po', 'tong', 'qian', 'fu', 'zhai', 'liu', 'qian', 'fu', 'li', 'yue', 'pi',
  0x60 => 'yang', 'ban', 'bo', 'jie', 'gou', 'shu', 'zheng', 'mu', 'xi', 'xi', 'di', 'jia', 'mu', 'tan', 'huan', 'yi',
  0x70 => 'si', 'kuang', 'ka', 'bei', 'jian', 'tong', 'xing', 'hong', 'jiao', 'chi', 'er', 'luo', 'bing', 'shi', 'mou', 'jia',
  0x80 => 'yin', 'jun', 'zhou', 'chong', 'xiang', 'tong', 'mo', 'lei', 'ji', 'yu', 'xu', 'ren', 'zun', 'zhi', 'qiong', 'shan',
  0x90 => 'chi', 'xian', 'xing', 'quan', 'pi', 'tie', 'zhu', 'xiang', 'ming', 'kua', 'yao', 'xian', 'xian', 'xiu', 'jun', 'cha',
  0xA0 => 'lao', 'ji', 'pi', 'ru', 'mi', 'yi', 'yin', 'guang', 'an', 'diu', 'you', 'se', 'kao', 'qian', 'luan', 'si',
  0xB0 => 'ai', 'diao', 'han', 'rui', 'shi', 'keng', 'qiu', 'xiao', 'zhe', 'xiu', 'zang', 'ti', 'cuo', 'gua', 'hong', 'zhong',
  0xC0 => 'tou', 'lu', 'mei', 'lang', 'wan', 'xin', 'yun', 'bei', 'wu', 'su', 'yu', 'chan', 'ding', 'bo', 'han', 'jia',
  0xD0 => 'hong', 'cuan', 'feng', 'chan', 'wan', 'zhi', 'si', 'xuan', 'hua', 'yu', 'tiao', 'gong', 'zhuo', 'lue', 'xing', 'qin',
  0xE0 => 'shen', 'han', 'lue', 'ye', 'chu', 'zeng', 'ju', 'xian', 'tie', 'mang', 'pu', 'li', 'pan', 'rui', 'cheng', 'gao',
  0xF0 => 'li', 'te', 'bing', 'zhu', 'zhen', 'tu', 'liu', 'zui', 'ju', 'chang', 'yuan', 'jian', 'gang', 'diao', 'tao', 'chang',
];
