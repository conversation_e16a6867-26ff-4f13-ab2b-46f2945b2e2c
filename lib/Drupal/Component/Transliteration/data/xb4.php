<?php

/**
 * @file
 * Generic transliteration data for the PhpTransliteration class.
 */

$base = [
  0x00 => 'dwaen', 'dwaenj', 'dwaenh', 'dwaed', 'dwael', 'dwaelg', 'dwaelm', 'dwaelb', 'dwaels', 'dwaelt', 'dwaelp', 'dwaelh', 'dwaem', 'dwaeb', 'dwaebs', 'dwaes',
  0x10 => 'dwaess', 'dwaeng', 'dwaej', 'dwaech', 'dwaek', 'dwaet', 'dwaep', 'dwaeh', 'doe', 'doeg', 'doekk', 'doegs', 'doen', 'doenj', 'doenh', 'doed',
  0x20 => 'doel', 'doelg', 'doelm', 'doelb', 'doels', 'doelt', 'doelp', 'doelh', 'doem', 'doeb', 'doebs', 'does', 'doess', 'doeng', 'doej', 'doech',
  0x30 => 'doek', 'doet', 'doep', 'doeh', 'dyo', 'dyog', 'dyokk', 'dyogs', 'dyon', 'dyonj', 'dyonh', 'dyod', 'dyol', 'dyolg', 'dyolm', 'dyolb',
  0x40 => 'dyols', 'dyolt', 'dyolp', 'dyolh', 'dyom', 'dyob', 'dyobs', 'dyos', 'dyoss', 'dyong', 'dyoj', 'dyoch', 'dyok', 'dyot', 'dyop', 'dyoh',
  0x50 => 'du', 'dug', 'dukk', 'dugs', 'dun', 'dunj', 'dunh', 'dud', 'dul', 'dulg', 'dulm', 'dulb', 'duls', 'dult', 'dulp', 'dulh',
  0x60 => 'dum', 'dub', 'dubs', 'dus', 'duss', 'dung', 'duj', 'duch', 'duk', 'dut', 'dup', 'duh', 'dwo', 'dwog', 'dwokk', 'dwogs',
  0x70 => 'dwon', 'dwonj', 'dwonh', 'dwod', 'dwol', 'dwolg', 'dwolm', 'dwolb', 'dwols', 'dwolt', 'dwolp', 'dwolh', 'dwom', 'dwob', 'dwobs', 'dwos',
  0x80 => 'dwoss', 'dwong', 'dwoj', 'dwoch', 'dwok', 'dwot', 'dwop', 'dwoh', 'dwe', 'dweg', 'dwekk', 'dwegs', 'dwen', 'dwenj', 'dwenh', 'dwed',
  0x90 => 'dwel', 'dwelg', 'dwelm', 'dwelb', 'dwels', 'dwelt', 'dwelp', 'dwelh', 'dwem', 'dweb', 'dwebs', 'dwes', 'dwess', 'dweng', 'dwej', 'dwech',
  0xA0 => 'dwek', 'dwet', 'dwep', 'dweh', 'dwi', 'dwig', 'dwikk', 'dwigs', 'dwin', 'dwinj', 'dwinh', 'dwid', 'dwil', 'dwilg', 'dwilm', 'dwilb',
  0xB0 => 'dwils', 'dwilt', 'dwilp', 'dwilh', 'dwim', 'dwib', 'dwibs', 'dwis', 'dwiss', 'dwing', 'dwij', 'dwich', 'dwik', 'dwit', 'dwip', 'dwih',
  0xC0 => 'dyu', 'dyug', 'dyukk', 'dyugs', 'dyun', 'dyunj', 'dyunh', 'dyud', 'dyul', 'dyulg', 'dyulm', 'dyulb', 'dyuls', 'dyult', 'dyulp', 'dyulh',
  0xD0 => 'dyum', 'dyub', 'dyubs', 'dyus', 'dyuss', 'dyung', 'dyuj', 'dyuch', 'dyuk', 'dyut', 'dyup', 'dyuh', 'deu', 'deug', 'deukk', 'deugs',
  0xE0 => 'deun', 'deunj', 'deunh', 'deud', 'deul', 'deulg', 'deulm', 'deulb', 'deuls', 'deult', 'deulp', 'deulh', 'deum', 'deub', 'deubs', 'deus',
  0xF0 => 'deuss', 'deung', 'deuj', 'deuch', 'deuk', 'deut', 'deup', 'deuh', 'dui', 'duig', 'duikk', 'duigs', 'duin', 'duinj', 'duinh', 'duid',
];
