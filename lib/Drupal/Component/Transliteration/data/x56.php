<?php

/**
 * @file
 * Generic transliteration data for the PhpTransliteration class.
 */

$base = [
  0x00 => 'di', 'qi', 'jiao', 'chong', 'jiao', 'kai', 'tan', 'shan', 'cao', 'jia', 'ai', 'xiao', 'piao', 'lou', 'ga', 'gu',
  0x10 => 'xiao', 'hu', 'hui', 'guo', 'ou', 'xian', 'ze', 'chang', 'xu', 'po', 'de', 'ma', 'ma', 'hu', 'lei', 'du',
  0x20 => 'ga', 'tang', 'ye', 'beng', 'ying', 'sai', 'jiao', 'mi', 'xiao', 'hua', 'mai', 'ran', 'chuai', 'peng', 'lao', 'xiao',
  0x30 => 'ji', 'zhu', 'chao', 'kui', 'zui', 'xiao', 'si', 'hao', 'fu', 'liao', 'qiao', 'xi', 'chu', 'chan', 'dan', 'hei',
  0x40 => 'xun', 'e', 'zun', 'fan', 'chi', 'hui', 'zan', 'chuang', 'cu', 'dan', 'yu', 'tun', 'ceng', 'jiao', 'ye', 'xi',
  0x50 => 'qi', 'hao', 'lian', 'xu', 'deng', 'hui', 'yin', 'pu', 'jue', 'qin', 'xun', 'nie', 'lu', 'si', 'yan', 'ying',
  0x60 => 'da', 'zhan', 'o', 'zhou', 'jin', 'nong', 'hui', 'xie', 'qi', 'e', 'zao', 'yi', 'shi', 'jiao', 'yuan', 'ai',
  0x70 => 'yong', 'jue', 'kuai', 'yu', 'pen', 'dao', 'ga', 'hm', 'dun', 'dang', 'xin', 'sai', 'pi', 'pi', 'yin', 'zui',
  0x80 => 'ning', 'di', 'lan', 'ta', 'huo', 'ru', 'hao', 'xia', 'ye', 'duo', 'pi', 'chou', 'ji', 'jin', 'hao', 'ti',
  0x90 => 'chang', 'xun', 'me', 'ca', 'ti', 'lu', 'hui', 'bo', 'you', 'nie', 'yin', 'hu', 'me', 'hong', 'zhe', 'li',
  0xA0 => 'liu', 'hai', 'nang', 'xiao', 'mo', 'yan', 'li', 'lu', 'long', 'mo', 'dan', 'chen', 'pin', 'pi', 'xiang', 'huo',
  0xB0 => 'mo', 'xi', 'duo', 'ku', 'yan', 'chan', 'ying', 'rang', 'dian', 'la', 'ta', 'xiao', 'jue', 'chuo', 'huan', 'huo',
  0xC0 => 'zhuan', 'nie', 'xiao', 'ca', 'li', 'chan', 'chai', 'li', 'yi', 'luo', 'nang', 'za', 'su', 'xi', 'zen', 'jian',
  0xD0 => 'za', 'zhu', 'lan', 'nie', 'nang', 'lan', 'lo', 'wei', 'hui', 'yin', 'qiu', 'si', 'nin', 'jian', 'hui', 'xin',
  0xE0 => 'yin', 'nan', 'tuan', 'tuan', 'dun', 'kang', 'yuan', 'jiong', 'pian', 'yun', 'cong', 'hu', 'hui', 'yuan', 'e', 'guo',
  0xF0 => 'kun', 'cong', 'tong', 'tu', 'wei', 'lun', 'guo', 'qun', 'ri', 'ling', 'gu', 'guo', 'tai', 'guo', 'tu', 'you',
];
