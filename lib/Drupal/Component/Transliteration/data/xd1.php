<?php

/**
 * @file
 * Generic transliteration data for the PhpTransliteration class.
 */

$base = [
  0x00 => 'tyal', 'tyalg', 'tyalm', 'tyalb', 'tyals', 'tyalt', 'tyalp', 'tyalh', 'tyam', 'tyab', 'tyabs', 'tyas', 'tyass', 'tyang', 'tyaj', 'tyach',
  0x10 => 'tyak', 'tyat', 'tyap', 'tyah', 'tyae', 'tyaeg', 'tyaekk', 'tyaegs', 'tyaen', 'tyaenj', 'tyaenh', 'tyaed', 'tyael', 'tyaelg', 'tyaelm', 'tyaelb',
  0x20 => 'tyaels', 'tyaelt', 'tyaelp', 'tyaelh', 'tyaem', 'tyaeb', 'tyaebs', 'tyaes', 'tyaess', 'tyaeng', 'tyaej', 'tyaech', 'tyaek', 'tyaet', 'tyaep', 'tyaeh',
  0x30 => 'teo', 'teog', 'teokk', 'teogs', 'teon', 'teonj', 'teonh', 'teod', 'teol', 'teolg', 'teolm', 'teolb', 'teols', 'teolt', 'teolp', 'teolh',
  0x40 => 'teom', 'teob', 'teobs', 'teos', 'teoss', 'teong', 'teoj', 'teoch', 'teok', 'teot', 'teop', 'teoh', 'te', 'teg', 'tekk', 'tegs',
  0x50 => 'ten', 'tenj', 'tenh', 'ted', 'tel', 'telg', 'telm', 'telb', 'tels', 'telt', 'telp', 'telh', 'tem', 'teb', 'tebs', 'tes',
  0x60 => 'tess', 'teng', 'tej', 'tech', 'tek', 'tet', 'tep', 'teh', 'tyeo', 'tyeog', 'tyeokk', 'tyeogs', 'tyeon', 'tyeonj', 'tyeonh', 'tyeod',
  0x70 => 'tyeol', 'tyeolg', 'tyeolm', 'tyeolb', 'tyeols', 'tyeolt', 'tyeolp', 'tyeolh', 'tyeom', 'tyeob', 'tyeobs', 'tyeos', 'tyeoss', 'tyeong', 'tyeoj', 'tyeoch',
  0x80 => 'tyeok', 'tyeot', 'tyeop', 'tyeoh', 'tye', 'tyeg', 'tyekk', 'tyegs', 'tyen', 'tyenj', 'tyenh', 'tyed', 'tyel', 'tyelg', 'tyelm', 'tyelb',
  0x90 => 'tyels', 'tyelt', 'tyelp', 'tyelh', 'tyem', 'tyeb', 'tyebs', 'tyes', 'tyess', 'tyeng', 'tyej', 'tyech', 'tyek', 'tyet', 'tyep', 'tyeh',
  0xA0 => 'to', 'tog', 'tokk', 'togs', 'ton', 'tonj', 'tonh', 'tod', 'tol', 'tolg', 'tolm', 'tolb', 'tols', 'tolt', 'tolp', 'tolh',
  0xB0 => 'tom', 'tob', 'tobs', 'tos', 'toss', 'tong', 'toj', 'toch', 'tok', 'tot', 'top', 'toh', 'twa', 'twag', 'twakk', 'twags',
  0xC0 => 'twan', 'twanj', 'twanh', 'twad', 'twal', 'twalg', 'twalm', 'twalb', 'twals', 'twalt', 'twalp', 'twalh', 'twam', 'twab', 'twabs', 'twas',
  0xD0 => 'twass', 'twang', 'twaj', 'twach', 'twak', 'twat', 'twap', 'twah', 'twae', 'twaeg', 'twaekk', 'twaegs', 'twaen', 'twaenj', 'twaenh', 'twaed',
  0xE0 => 'twael', 'twaelg', 'twaelm', 'twaelb', 'twaels', 'twaelt', 'twaelp', 'twaelh', 'twaem', 'twaeb', 'twaebs', 'twaes', 'twaess', 'twaeng', 'twaej', 'twaech',
  0xF0 => 'twaek', 'twaet', 'twaep', 'twaeh', 'toe', 'toeg', 'toekk', 'toegs', 'toen', 'toenj', 'toenh', 'toed', 'toel', 'toelg', 'toelm', 'toelb',
];
