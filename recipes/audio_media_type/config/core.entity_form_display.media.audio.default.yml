langcode: en
status: true
dependencies:
  config:
    - field.field.media.audio.field_media_audio_file
    - media.type.audio
  module:
    - file
    - path
id: media.audio.default
targetEntityType: media
bundle: audio
mode: default
content:
  created:
    type: datetime_timestamp
    weight: 10
    region: content
    settings: {  }
    third_party_settings: {  }
  field_media_audio_file:
    type: file_generic
    weight: 0
    region: content
    settings:
      progress_indicator: throbber
    third_party_settings: {  }
  path:
    type: path
    weight: 30
    region: content
    settings: {  }
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 100
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  uid:
    type: entity_reference_autocomplete
    weight: 5
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
hidden:
  name: true
