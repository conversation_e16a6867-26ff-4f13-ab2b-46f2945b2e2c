entity.action.collection:
  path: '/admin/config/system/actions'
  defaults:
    _title: 'Actions'
    _entity_list: 'action'
  requirements:
    _permission: 'administer actions'

action.admin_add:
  path: '/admin/config/system/actions/add/{action_id}'
  defaults:
    _entity_form: 'action.add'
    _title: 'Add action'
  requirements:
    _permission: 'administer actions'

entity.action.edit_form:
  path: '/admin/config/system/actions/configure/{action}'
  defaults:
    _entity_form: 'action.edit'
    _title: 'Edit action'
  requirements:
    _permission: 'administer actions'

entity.action.delete_form:
  path: '/admin/config/system/actions/configure/{action}/delete'
  defaults:
    _entity_form: 'action.delete'
    _title: 'Delete action'
  requirements:
    _permission: 'administer actions'
