langcode: en
status: true
dependencies:
  module:
    - user
id: test_http_status_code
label: test_http_status_code
module: views
description: ''
tag: ''
base_table: node_field_data
base_field: nid
display:
  default:
    display_plugin: default
    id: default
    display_title: Default
    position: null
    display_options:
      access:
        type: perm
      cache:
        type: tag
      query:
        type: views_query
      exposed_form:
        type: basic
      pager:
        type: full
      style:
        type: default
      row:
        type: fields
      fields:
        title:
          id: title
          table: node_field_data
          field: title
          label: ''
          alter:
            alter_text: false
            make_link: false
            absolute: false
            trim: false
            word_boundary: false
            ellipsis: false
            strip_tags: false
            html: false
          hide_empty: false
          empty_zero: false
          plugin_id: field
          entity_type: node
          entity_field: title
      filters:
        status:
          value: '1'
          table: node_field_data
          field: status
          id: status
          expose:
            operator: '0'
          group: 1
          plugin_id: boolean
          entity_type: node
          entity_field: status
      sorts:
        created:
          id: created
          table: node_field_data
          field: created
          order: DESC
          entity_type: node
          entity_field: created
      empty:
        http_status_code:
          id: http_status_code
          table: views
          field: http_status_code
          relationship: none
          group_type: group
          admin_label: ''
          label: ''
          empty: true
          status_code: 200
          plugin_id: http_status_code
  page_1:
    display_plugin: page
    id: page_1
    display_title: Page
    position: null
    display_options:
      path: test-http-status-code
