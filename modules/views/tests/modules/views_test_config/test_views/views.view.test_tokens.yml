langcode: en
status: true
dependencies: {  }
id: test_tokens
label: 'Test tokens'
module: views
description: 'Test view to token replacement tests.'
tag: ''
base_table: views_test_data
base_field: id
display:
  default:
    display_options:
      title: 'Test token default'
      defaults:
        fields: false
        pager: false
        sorts: false
      fields:
        age:
          field: age
          id: age
          relationship: none
          table: views_test_data
          plugin_id: numeric
        id:
          field: id
          id: id
          relationship: none
          table: views_test_data
          plugin_id: numeric
        name:
          field: name
          id: name
          relationship: none
          table: views_test_data
          plugin_id: string
      pager:
        type: full
        options:
          items_per_page: 10
    display_plugin: default
    display_title: Default
    id: default
    position: 0
  page_1:
    id: page_1
    display_title: Page
    display_plugin: page
    position: 1
    display_options:
      defaults:
        title: false
      title: 'Test token page'
      query:
        type: views_query
        options: {  }
      path: test_tokens
  page_2:
    id: page_2
    display_title: Page
    display_plugin: page
    position: 2
    display_options:
      defaults:
        filters: false
      query:
        type: views_query
        options: {  }
      filters:
        name:
          field: name
          id: test_filter
          table: views_test_data
          plugin_id: string
          operator: '='
          value: 'not an existing name'
      path: test_tokens_empty
  page_3:
    display_plugin: page
    id: page_3
    display_title: Page
    position: 1
    display_options:
      defaults:
        title: false
        pager: false
        header: false
      pager:
        type: mini
        options:
          items_per_page: 2
      title: 'Test token page with mini pager'
      path: test_tokens_mini_pager
      display_extenders: {  }
      header:
        area:
          id: area
          table: views
          field: area
          relationship: none
          group_type: group
          admin_label: ''
          empty: false
          tokenize: false
          content:
            value: "Total rows: [view:total-rows] - Page count: [view:page-count]"
            format: basic_html
          plugin_id: text
  page_4:
    display_plugin: page
    id: page_4
    display_title: Specific items page
    position: 5
    display_options:
      defaults:
        title: false
        pager: false
        header: false
      pager:
        type: some
        options:
          offset: 0
          items_per_page: 3
      title: 'Test token page with "Display a specified number of items" pager plugin.'
      path: test_tokens_display_specific_items
      display_extenders: { }
      header:
        area:
          id: area
          table: views
          field: area
          relationship: none
          group_type: group
          admin_label: ''
          empty: false
          tokenize: false
          content:
            value: "Total rows: [view:total-rows]"
            format: basic_html
          plugin_id: text
