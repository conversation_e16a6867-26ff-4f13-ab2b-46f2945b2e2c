# cspell:ignore localizable
id: d6_language_content_taxonomy_vocabulary_settings
label: Drupal 6 language taxonomy vocabulary settings
migration_tags:
  - Drupal 6
  - Configuration
source:
  plugin: d6_language_content_settings_taxonomy_vocabulary
  constants:
    target_type: 'taxonomy_term'
    default_langcode: 'site_default'
process:
  target_bundle:
    -
      plugin: migration_lookup
      migration: d6_taxonomy_vocabulary
      source: vid
    -
      plugin: skip_on_empty
      method: row
  # State is the value in the i18ntaxonomy_vocabulary array defined as:
  # 0: No multilingual options.
  # 1: Localizable terms. Run through the localization system.
  # 2: Predefined language for a vocabulary and its terms.
  # 3: Per-language terms, translatable (referencing terms with different
  #  languages) but not localizable.
  language_alterable:
    plugin: static_map
    source: state
    map:
      0: false
      1: true
      2: false
      3: true
  'third_party_settings/content_translation/enabled':
    plugin: static_map
    source: state
    map:
      0: false
      1: true
      2: false
      3: false
  target_entity_type_id: 'constants/target_type'
  default_langcode:
    plugin: default_value
    default_value: site_default
    source: language
destination:
  plugin: entity:language_content_settings
migration_dependencies:
  required:
    - d6_taxonomy_vocabulary
    - language
