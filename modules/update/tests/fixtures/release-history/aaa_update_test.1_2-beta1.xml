<?xml version="1.0" encoding="utf-8"?>
<project xmlns:dc="http://purl.org/dc/elements/1.1/">
<title>AAA Update test</title>
<short_name>aaa_update_test</short_name>
<dc:creator>Drupal</dc:creator>
<supported_branches>8.x-1.</supported_branches>
<project_status>published</project_status>
<link>http://example.com/project/aaa_update_test</link>
  <terms>
   <term><name>Projects</name><value>Modules</value></term>
  </terms>
<releases>
  <release>
    <!-- This release is not in a supported branch; therefore it should not be recommended. -->
    <name>aaa_update_test 8.x-3.0</name>
    <version>8.x-3.0</version>
    <status>published</status>
    <release_link>http://example.com/aaa_update_test-8-x-3-0-release</release_link>
    <download_link>http://example.com/aaa_update_test-8.x-3.0.tar.gz</download_link>
    <date>1250426521</date>
    <terms>
      <term><name>Release type</name><value>New features</value></term>
      <term><name>Release type</name><value>Bug fixes</value></term>
    </terms>
  </release>
  <release>
    <name>aaa_update_test 8.x-1.2-beta1</name>
    <version>8.x-1.2-beta1</version>
    <status>published</status>
    <release_link>http://example.com/aaa_update_test-8-x-1-2-beta1-release</release_link>
    <download_link>http://example.com/aaa_update_test-8.x-1.2-beta1.tar.gz</download_link>
    <date>1250412521</date>
    <terms>
      <term><name>Release type</name><value>New features</value></term>
      <term><name>Release type</name><value>Bug fixes</value></term>
    </terms>
  </release>
  <release>
    <name>aaa_update_test 8.x-1.2-alpha1</name>
    <version>8.x-1.2-alpha1</version>
    <status>published</status>
    <release_link>http://example.com/aaa_update_test-8-x-1-2-alpha1-release</release_link>
    <download_link>http://example.com/aaa_update_test-8.x-1.2-alpha1.tar.gz</download_link>
    <date>1250413521</date>
    <terms>
      <term><name>Release type</name><value>New features</value></term>
      <term><name>Release type</name><value>Bug fixes</value></term>
    </terms>
  </release>
  <release>
    <name>aaa_update_test 8.x-1.1</name>
    <version>8.x-1.1</version>
    <status>published</status>
    <release_link>http://example.com/aaa_update_test-8-x-1-1-release</release_link>
    <download_link>http://example.com/aaa_update_test-8.x-1.1.tar.gz</download_link>
    <date>1250424521</date>
    <terms>
      <term><name>Release type</name><value>New features</value></term>
      <term><name>Release type</name><value>Bug fixes</value></term>
    </terms>
  </release>
  <release>
    <name>aaa_update_test 8.x-1.1-beta1</name>
    <version>8.x-1.1-beta1</version>
    <status>published</status>
    <release_link>http://example.com/aaa_update_test-8-x-1-1-beta1-release</release_link>
    <download_link>http://example.com/aaa_update_test-8.x-1.1-beta1.tar.gz</download_link>
    <date>1250414521</date>
    <terms>
      <term><name>Release type</name><value>New features</value></term>
      <term><name>Release type</name><value>Bug fixes</value></term>
    </terms>
  </release>
  <release>
    <name>aaa_update_test 8.x-1.1-alpha1</name>
    <version>8.x-1.1-alpha1</version>
    <status>published</status>
    <release_link>http://example.com/aaa_update_test-8-x-1-1-alpha1-release</release_link>
    <download_link>http://example.com/aaa_update_test-8.x-1.1-alpha1.tar.gz</download_link>
    <date>1250414521</date>
    <terms>
      <term><name>Release type</name><value>New features</value></term>
      <term><name>Release type</name><value>Bug fixes</value></term>
    </terms>
  </release>
  <release>
    <name>aaa_update_test 8.x-1.0</name>
    <version>8.x-1.0</version>
    <status>published</status>
    <release_link>http://example.com/aaa_update_test-8-x-1-0-release</release_link>
    <download_link>http://example.com/aaa_update_test-8.x-1.0.tar.gz</download_link>
    <date>1250404521</date>
    <terms>
      <term><name>Release type</name><value>New features</value></term>
      <term><name>Release type</name><value>Bug fixes</value></term>
    </terms>
  </release>
</releases>
</project>
