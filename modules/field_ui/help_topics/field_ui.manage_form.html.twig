---
label: 'Configuring the edit form for an entity sub-type'
related:
  - core.content_structure
  - field_ui.add_field
  - field_ui.manage_display
  - core.ui_accessibility
---
{% set content_types_link_text %}{% trans %}Content types{% endtrans %}{% endset %}
{% set content_types_link = render_var(help_route_link(content_types_link_text, 'entity.node_type.collection')) %}
{% set content_structure_topic = render_var(help_topic_link('core.content_structure')) %}
<h2>{% trans %}Goal{% endtrans %}</h2>
<p>{% trans %}Configure the <em>widgets</em> used to edit the fields of an entity sub-type, their order on the form, and the widget settings. See {{ content_structure_topic }} for background information.{% endtrans %}</p>
<h2>{% trans %}Steps{% endtrans %}</h2>
<ol>
  <li>{% trans %}Navigate to the page for managing the entity type you want to add the field to. For example, to add a field to a content type, in the <em>Manage</em> administrative menu, navigate to <em>Structure</em> &gt; <em>{{ content_types_link }}</em>.{% endtrans %}</li>
  <li>{% trans %}Find the particular sub-type that you want to configure the editing form for, and click <em>Manage form display</em> in the <em>Operations</em> list.{% endtrans %}</li>
  <li>{% trans %}Use the drag arrows to order the fields in your preferred order.{% endtrans %}</li>
  <li>{% trans %}Drag any fields that you do not wish to see on the editing form to the <em>Disabled</em> section.{% endtrans %}</li>
  <li>{% trans %}In the <em>Widget</em> column, select the widget for editing each field.{% endtrans %}</li>
  <li>{% trans %}After selecting the desired widgets, click the settings gear in each row to change the settings for the widget.{% endtrans %}</li>
  <li>{% trans %}When you are done making changes, click <em>Save</em>.{% endtrans %}</li>
  <li>{% trans %}Test the editing form for your entity sub-type by editing or creating an entity. If needed, return to these steps to further refine the form.{% endtrans %}</li>
</ol>
