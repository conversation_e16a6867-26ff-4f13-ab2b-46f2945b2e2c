<?php

namespace Drupal\schema_special_announcement\Plugin\metatag\Tag;

use <PERSON>upal\schema_metatag\Plugin\metatag\Tag\SchemaNameBase;

/**
 * Provides a plugin for the 'name' meta tag.
 *
 * - 'id' should be a globally unique id.
 * - 'name' should match the Schema.org element name.
 * - 'group' should match the id of the group that defines the Schema.org type.
 *
 * @MetatagTag(
 *   id = "schema_special_announcement_name",
 *   label = @Translation("name"),
 *   description = @Translation("The short title of the COVID-19 announcement. For example: 'Stanford announces COVID-19 testing facility'."),
 *   name = "name",
 *   group = "schema_special_announcement",
 *   weight = 0,
 *   type = "string",
 *   secure = FALSE,
 *   multiple = FALSE,
 *   property_type = "text",
 *   tree_parent = {},
 *   tree_depth = -1,
 * )
 */
class SchemaSpecialAnnouncementName extends SchemaNameBase {

}
