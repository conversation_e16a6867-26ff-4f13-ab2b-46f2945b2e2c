langcode: en
status: true
dependencies:
  config:
    - block_content.type.basic
    - field.field.block_content.basic.body
  module:
    - text
id: block_content.basic.default
targetEntityType: block_content
bundle: basic
mode: default
content:
  body:
    type: text_default
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 0
    region: content
hidden:
  langcode: true
