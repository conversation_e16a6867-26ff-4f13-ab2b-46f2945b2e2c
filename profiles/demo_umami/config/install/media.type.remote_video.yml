langcode: en
status: true
dependencies: {  }
id: remote_video
label: 'Remote video'
description: 'A remotely hosted video from YouTube or Vimeo.'
source: 'oembed:video'
queue_thumbnail_downloads: false
new_revision: true
source_configuration:
  source_field: field_media_oembed_video
  thumbnails_directory: 'public://oembed_thumbnails/[date:custom:Y-m]'
  providers:
    - YouTube
    - Vimeo
field_map:
  title: name
