<?php

use <PERSON><PERSON>al\Core\DrupalKernel;
use Symfony\Component\HttpFoundation\Request;
use Drupal\menu_link_content\Entity\MenuLinkContent;

// Bootstrap Drupal
$autoloader = require_once 'vendor/autoload.php';
$kernel = new DrupalKernel('prod', $autoloader);
$request = Request::createFromGlobals();
$response = $kernel->handle($request);
$kernel->boot();

// Load the specific menu link (ID 327 - "Letnia Kampania Szkoleniowa")
$menu_link = MenuLinkContent::load(327);

if ($menu_link) {
  echo "Found menu link: " . $menu_link->getTitle() . "\n";
  echo "Current URI: " . $menu_link->link->first()->uri . "\n";
  
  $link_options = $menu_link->link->first()->options;
  echo "Current options: " . print_r($link_options, true) . "\n";
  
  // Update the menu link
  $menu_link->set('title', 'Voucher na szkolenia');
  $menu_link->set('link', [
    'uri' => 'https://4grow.pl/voucher-szkoleniowy',
    'options' => $link_options // Keep existing options including class
  ]);
  
  $menu_link->save();
  
  echo "Successfully updated menu link to 'Voucher na szkolenia'\n";
  echo "New URI: https://4grow.pl/voucher-szkoleniowy\n";
} else {
  echo "Menu link with ID 327 not found!\n";
}

echo "Script completed.\n";
