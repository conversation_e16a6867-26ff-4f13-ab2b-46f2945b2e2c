<?php

namespace Drupal\npx_freshmail\Plugin\Block;

use <PERSON><PERSON><PERSON>\Core\Block\BlockBase;
use <PERSON><PERSON>al\Core\Form\FormStateInterface;
use <PERSON><PERSON>al\Core\Plugin\ContainerFactoryPluginInterface;
use <PERSON>ymfony\Component\DependencyInjection\ContainerInterface;
use <PERSON><PERSON>al\Core\Form\FormBuilderInterface;
use <PERSON><PERSON>al\npx_freshmail\NpxFreshmailManager;

/**
 * Provides a 'NpxFreshmailSubscribeBlock' block.
 *
 * @Block(
 *  id = "npx_freshmail_subscribe_block",
 *  admin_label = @Translation("Npx freshmail subscribe block"),
 * )
 */
class NpxFreshmailSubscribeBlock extends BlockBase implements ContainerFactoryPluginInterface {

  /**
   * Drupal\Core\Form\FormBuilderInterface definition.
   *
   * @var \Drupal\Core\Form\FormBuilderInterface
   */
  protected $formBuilder;
  
  /**
   * Drupal\npx_freshmail\NpxFreshmailManager definition.
   *
   * @var \Drupal\npx_freshmail\NpxFreshmailManager
   */
  protected $npxFreshmailManager;
  
  /**
   * Constructs a new NpxFreshmailSubscribeBlock object.
   *
   * @param array $configuration
   *   A configuration array containing information about the plugin instance.
   * @param string $plugin_id
   *   The plugin_id for the plugin instance.
   * @param string $plugin_definition
   *   The plugin implementation definition.
   */
  public function __construct(
    array $configuration,
    $plugin_id,
    $plugin_definition,
    FormBuilderInterface $form_builder,
    NpxFreshmailManager $npx_freshmail_manager
  ) {
    parent::__construct($configuration, $plugin_id, $plugin_definition);
    $this->formBuilder = $form_builder;
    $this->npxFreshmailManager = $npx_freshmail_manager;
  }
  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container, array $configuration, $plugin_id, $plugin_definition) {
    return new static(
      $configuration,
      $plugin_id,
      $plugin_definition,
      $container->get('form_builder'),
      $container->get('npx_freshmail.manager')
    );
  }
  /**
   * {@inheritdoc}
   */
  public function defaultConfiguration() {
    return [
          ] + parent::defaultConfiguration();
  }

  /**
   * {@inheritdoc}
   */
  public function blockForm($form, FormStateInterface $form_state) {
    $lists = $this->npxFreshmailManager->getFreshmailLists();
    
    $options = [];
    
    /* \Drupal\npx_freshmail\NpxFreshmailList $list */
    foreach($lists as $list) {
      $options[$list->getTermId()] = $list->getTitle();
    }
    
    $form['freshmail_lists'] = [
      '#type' => 'checkboxes',
      '#title' => $this->t('Freshmail Listy'),
      '#options' => $options,
      '#default_value' => $this->configuration['freshmail_lists'],
      '#required' => true,
      '#weight' => '0',
    ];
    
    $form['header_number'] = [
      '#type' => 'textfield',
      '#title' => 'Nagłówek',
      '#default_value' => $this->configuration['header_number'],
    ];
    
    $form['content_text'] = [
      '#type' => 'text_format',
      '#title' => 'Tekst pod nagłówkiem',
      '#default_value' => $this->configuration['content_text']['value'],
      '#format' => $this->configuration['content_text']['format'] ?? 'full_html',
    ];
    $form['content_message'] = [
      '#type' => 'text_format',
      '#title' => 'Podziękowanie',
      '#default_value' => $this->configuration['content_message']['value'],
      '#format' => $this->configuration['content_message']['format'] ?? 'full_html',
    ];
    $form['btn_label'] = [
      '#type' => 'textfield',
      '#title' => 'Przycisk',
      '#default_value' => $this->configuration['btn_label'],
    ];
    
    return $form;
  }

  /**
   * {@inheritdoc}
   */
  public function blockSubmit($form, FormStateInterface $form_state) {
    $this->configuration['freshmail_lists'] = $form_state->getValue('freshmail_lists');
    $this->configuration['header_number'] = $form_state->getValue('header_number');
    $this->configuration['content_text'] = $form_state->getValue('content_text');
    $this->configuration['content_message'] = $form_state->getValue('content_message');
    $this->configuration['btn_label'] = $form_state->getValue('btn_label');
  }

  /**
   * {@inheritdoc}
   */
  public function build() {
    $build = [];
    
    $configuration = [
      'freshmail_lists' => $this->configuration['freshmail_lists'],
      'submit_btn' => $this->configuration['btn_label'],
      'lists_label' => 'Wybierz swoje korzyści',
    ];

    $image_source = \Drupal::service('extension.list.module')->getPath('npx_freshmail') . '/img/Icon_newsletter_stopka.png';

//     $number = $this->npxFreshmailManager->getTotalSubscribersCount();
    $header = $this->configuration['header_number'];
    
    $build['freshmail_block'] = [
      '#theme' => 'npx_freshmail_block_footer',
      '#image' => $image_source,
      '#msg' => $this->configuration['content_message']['value'],
      '#header' => $header,
      '#text' => $this->configuration['content_text']['value'],
      '#form_config' => $configuration,
    ];
    $build['#attached']['library'][] = 'npx_freshmail/all';
    $build['#attached']['library'][] = 'npx_freshmail/footer';
    
    return $build;
  }
}
