langcode: pl
status: true
dependencies:
  config:
    - field.storage.taxonomy_term.field_npx_freshmail_list_id
    - taxonomy.vocabulary.npx_freshmail_lists
id: taxonomy_term.npx_freshmail_lists.field_npx_freshmail_list_id
field_name: field_npx_freshmail_list_id
entity_type: taxonomy_term
bundle: npx_freshmail_lists
label: 'ID listy'
description: 'Identyfikator listy z freshmaila.'
required: true
translatable: false
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: string