/**
 * @file
 * Attaches the behaviors for the Npx notify.
 */

(function ($, Drupal, drupalSettings, once) {
  "use strict";

  Drupal.behaviors.npxNotifyMessageForm = {
    attach: function (context) {
      var $btns = $("a.npx-token-toggle");
      $btns.each(function () {
        const messageFormElements = once(
          "npx-notify-message-form",
          this,
          document
        );
        messageFormElements.forEach(function (element) {
          $(element).click(function (e) {
            e.preventDefault();
            $(this).closest("div").find("code").toggleClass("hidden");
          });
        });
      });
    },
  };
})(jQuery, Drupal, drupalSettings, once);
