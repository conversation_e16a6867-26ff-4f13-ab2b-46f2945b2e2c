<?php

/**
 * @file
 * Contains \Drupal\npx_pdf\Controller\PDFController.
 */

namespace Drupal\npx_pdf\Controller;

use <PERSON><PERSON>al\Core\Controller\ControllerBase;
use Symfony\Component\DependencyInjection\ContainerInterface;
use <PERSON><PERSON><PERSON>\Core\Entity\EntityManager;
use <PERSON><PERSON>al\npx_pdf\PDFHelper;
use Drupal\Component\Utility\Html;
use <PERSON><PERSON>al\npx_notify\NotifyHelper;
use Drupal\node\Entity\Node;
use Drupal\Core\Url;
use Drupal\Core\Link;

/**
 * Class PDFController.
 *
 * @package Drupal\npx_pdf\Controller
 */
class PDFController extends ControllerBase {

  /**
   * Drupal\Core\Entity\EntityManager definition.
   *
   * @var Drupal\Core\Entity\EntityManager
   */
  protected $entity_manager;
  /**
   * {@inheritdoc}
   */
  public function __construct(EntityManager $entity_manager) {
    $this->entity_manager = $entity_manager;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container) {
    return new static(
      $container->get('entity_type.manager')
    );
  }

  /**
   * Generatepdf.
   *
   * @return string
   *   Return Hello string.
   */
  public function generatePDF($id) {
//     $html = PDFHelper::prepareHtml($id);
//     PDFHelper::streamPdf($html, 'zgloszenie_test');
//     $data = PDFHelper::getPdfData($html);
//     $file = file_save_data($data, 'private://zgloszenie_test.pdf', FILE_EXISTS_REPLACE);
//     $container = \Drupal::getContainer();
//     $submission = $container->get('entity.manager')->getStorage('npxsubmission')->load($id);
//     $submission->field_npxsubmission_pdf->setValue(['target_id' => $file->id()]);
//     $submission->save();
//    NotifyHelper::createNotifications($submission);
//    NotifyHelper::prepareNotificationQueue();

    
    //////// IMPORT ////////
//      $filepath = 'http://slot5.dummysite.eu/' . drupal_get_path('module', 'npx_pdf') . '/followup.json';
//      $data = file_get_contents($filepath);
//      $json = json_decode($data, true);
// //     dpm($json[0]);
// //
// return;
//      foreach ($json as $elem) {
//        //$elem = $json[0];
//        $langcode = \Drupal::languageManager()->getDefaultLanguage()->getId();
//        $tags = explode(', ', $elem['tagi']);
//        $node = Node::create([
//            'type' => 'followup',
//            'langcode' => $langcode,
//            'created' => REQUEST_TIME,
//            'changed' => REQUEST_TIME,
//            'uid' => 1,
//            'title' => $elem['title'],
//            'body' => [
//                'summary' => '',
//                'value' => $elem['field_oferta_tresc'],
//                'format' => 'full_html',
//            ],
//            'field_tagi' => $tags,
//        ]);
//        $node->save();
//        \Drupal::service('path.alias_storage')->save("/node/" . $node->id(), substr($elem['path'], 11), $langcode);
//        //return;
//      }
//      return ['#markup' => count($json)];
//     $link = Link::createFromRoute('Edytuj zgłoszenie', 'entity.npxsubmission.edit_form', ['npxsubmission' => 15]);
//     return ['#markup' => render($link->toRenderable())];
    return ['#markup' => 'Zablokowane'];
  }
}
