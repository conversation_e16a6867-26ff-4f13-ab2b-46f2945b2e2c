langcode: pl
status: true
dependencies:
  config:
    - field.storage.taxonomy_term.field_reverse_score
    - taxonomy.vocabulary.kategorie_pytan
id: taxonomy_term.kategorie_pytan.field_reverse_score
field_name: field_reverse_score
entity_type: taxonomy_term
bundle: kategorie_pytan
label: 'Odwróć wynik'
description: ''
required: false
translatable: false
default_value:
  -
    value: 0
default_value_callback: ''
settings:
  on_label: Odwrócone
  off_label: Normalne
field_type: boolean