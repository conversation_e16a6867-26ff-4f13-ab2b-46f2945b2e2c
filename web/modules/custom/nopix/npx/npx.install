<?php

use <PERSON><PERSON>al\field\Entity\FieldConfig;
use Drupal\field\Entity\FieldStorageConfig;
use Drupal\Core\Config\FileStorage;
use Drupal\menu_link_content\Entity\MenuLinkContent;

/**
 * Implements hook_update_N() on Module npx Update # 8001.
 * Install field_npxtraining_date_form
 */
function npx_update_8001(&$sandbox) {
  $config_path = \Drupal::service('extension.path.resolver')->getPath('module', 'npx') . '/config/install';
  $storage = new FileStorage($config_path);

  $data = $storage->read('field.storage.node.field_npxtraining_date_form');
  if (!FieldStorageConfig::loadByName($data['entity_type'], $data['field_name'])) {
    FieldStorageConfig::create($data)->save();
  }

  $data = $storage->read('field.field.node.npxtraining_date.field_npxtraining_date_form');
  if (!FieldConfig::loadByName($data['entity_type'], $data['bundle'], $data['field_name'])) {
    FieldConfig::create($data)->save();
  }
}

/**
 * Implements hook_update_N() on Module npx Update # 10020.
 * Update menu link from "oferty specjalne na lato" to "Voucher na szkolenia"
 */
function npx_update_10020(&$sandbox) {
  $query = \Drupal::entityQuery('menu_link_content')
    ->condition('menu_name', 'grow3menu')
    ->accessCheck(FALSE);

  $menu_link_ids = $query->execute();

  foreach ($menu_link_ids as $menu_link_id) {
    $menu_link = MenuLinkContent::load($menu_link_id);

    if ($menu_link) {
      $link_options = $menu_link->link->first()->options;

      if (isset($link_options['attributes']['class']) &&
          in_array('menu-link-promocje', $link_options['attributes']['class'])) {

        $menu_link->set('title', 'Voucher na szkolenia');
        $menu_link->set('link', [
          'uri' => 'https://4grow.pl/voucher-szkoleniowy',
          'options' => $link_options
        ]);

        $menu_link->save();

        \Drupal::logger('npx')->info('Updated menu link to Voucher na szkolenia');
        break;
      }
    }
  }
}
