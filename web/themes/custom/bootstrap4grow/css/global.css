/* VARIABLES */
/* TYPOGRAPHY */
/* Google Fonts */
:root {
  --secondary: #0066cc;
}

/* VARIABLES */
/* TYPOGRAPHY */
/* Google Fonts */
:root {
  --secondary: #0066cc;
}

.nav {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}

.nav-link {
  display: block;
  padding: 0.5rem 1rem;
  color: #0053B3;
  -webkit-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
  -o-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .nav-link {
    -webkit-transition: none;
    -o-transition: none;
    transition: none;
  }
}
.nav-link:hover, .nav-link:focus {
  color: #005283;
  text-decoration: none;
}
.nav-link.disabled {
  color: #6c757d;
  pointer-events: none;
  cursor: default;
}

.nav-tabs {
  border-bottom: 1px solid #dee2e6;
}
.nav-tabs .nav-link {
  margin-bottom: -0.0625rem;
  background: none;
  border: 1px solid transparent;
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
}
.nav-tabs .nav-link:hover, .nav-tabs .nav-link:focus {
  border-color: #e9ecef #e9ecef #dee2e6;
  isolation: isolate;
}
.nav-tabs .nav-link.disabled {
  color: #6c757d;
  background-color: transparent;
  border-color: transparent;
}
.nav-tabs .nav-link.active,
.nav-tabs .nav-item.show .nav-link {
  color: #495057;
  background-color: #fff;
  border-color: #dee2e6 #dee2e6 #fff;
}
.nav-tabs .dropdown-menu {
  margin-top: -0.0625rem;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.nav-pills .nav-link {
  background: none;
  border: 0;
  border-radius: 0.25rem;
}
.nav-pills .nav-link.active,
.nav-pills .show > .nav-link {
  color: #fff;
  background-color: #0d6efd;
}

.nav-fill > .nav-link,
.nav-fill .nav-item {
  -webkit-box-flex: 1;
  -webkit-flex: 1 1 auto;
      -ms-flex: 1 1 auto;
          flex: 1 1 auto;
  text-align: center;
}

.nav-justified > .nav-link,
.nav-justified .nav-item {
  -webkit-flex-basis: 0;
      -ms-flex-preferred-size: 0;
          flex-basis: 0;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
  text-align: center;
}

.nav-fill .nav-item .nav-link,
.nav-justified .nav-item .nav-link {
  width: 100%;
}

.tab-content > .tab-pane {
  display: none;
}
.tab-content > .active {
  display: block;
}

#block-bootstrap4grow-grow3menu-2 {
  min-height: auto;
  display: inline-grid;
}

li.n-online-info-wrapper {
  float: none;
  max-width: 1415px;
  margin: 0 auto !important;
  -webkit-box-sizing: initial !important;
          box-sizing: initial !important;
  cursor: auto !important;
}

.n-online-info-wrapper .n-online-info {
  display: block;
  font-weight: bold;
  margin: 0.5rem 0;
  text-transform: uppercase;
  font-size: 0.8125rem;
}

.sf-accordion .n-online-info {
  font-size: 0.8125rem;
}

#block-grow3menu ul#superfish-grow3menu > li > ul > li.sf-depth-2.menuparent {
  -webkit-box-sizing: initial;
          box-sizing: initial;
}

.n-menu-red {
  color: red;
  text-transform: uppercase;
  font-weight: bold;
  padding-bottom: 0.5rem;
  display: block;
}

.block-superfishgrow3menu .sf-depth-1.menuparent {
  cursor: default;
}

ul.sf-menu span.n-menu-red {
  margin-left: -0.9375rem;
  width: calc(100vw - 15px);
  padding-bottom: 0.625rem;
  padding-top: 0.625rem;
}

ul#superfish-grow3menu-accordion {
  border-top: 1px solid #d0d8db;
  background: #fff;
}
ul#superfish-grow3menu-accordion .n-menu-search-wrapper-li {
  display: none;
}
ul#superfish-grow3menu-accordion a.sf-depth-1 {
  font-weight: 800;
  border-bottom: 1px solid #e3e9e9;
  text-transform: uppercase;
  padding: 1.25rem 0;
}
ul#superfish-grow3menu-accordion li.sf-expanded > a {
  border: 0;
}
ul#superfish-grow3menu-accordion li.sf-expanded.sf-depth-1 ul {
  border-top: 1px solid #e3e9e9;
}
ul#superfish-grow3menu-accordion a.sf-depth-2 {
  font-weight: 500;
}
ul#superfish-grow3menu-accordion .sf-clone-parent {
  display: none;
}
ul#superfish-grow3menu-accordion a.menuparent:after {
  content: "";
  width: 13px;
  height: 7px;
  display: inline-block;
  background: transparent no-repeat url("/themes/custom/bootstrap4grow/images/menu-arrow.png") 0 0;
  margin-left: 0.625rem;
  -webkit-transition: -webkit-transform 300ms ease;
  transition: -webkit-transform 300ms ease;
  -o-transition: -o-transform 300ms ease;
  transition: transform 300ms ease;
  transition: transform 300ms ease, -webkit-transform 300ms ease, -o-transform 300ms ease;
  -webkit-transform: scaleY(-1);
       -o-transform: scaleY(-1);
          transform: scaleY(-1);
}
ul#superfish-grow3menu-accordion a {
  padding: 0.625rem;
  line-height: 1.25rem;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
ul#superfish-grow3menu-accordion a.menuparent:hover:after {
  -webkit-transform: scaleY(1);
       -o-transform: scaleY(1);
          transform: scaleY(1);
}
ul#superfish-grow3menu-accordion a:not(.sf-depth-4) {
  color: #000;
}
ul#superfish-grow3menu-accordion.sf-expanded {
  display: block;
  left: 0 !important;
  width: 100%;
  position: absolute;
  top: 66px !important;
}
ul#superfish-grow3menu-accordion.sf-expanded li {
  padding-left: 0.9375rem;
  padding-right: 0.9375rem;
  max-width: 100%;
}

ul#superfish-grow3menu a.menuparent:hover::after {
  -webkit-transform: scaleY(1);
  -o-transform: scaleY(1);
     transform: scaleY(1);
}
ul#superfish-grow3menu li.sf-depth-1:not(:first-child) > ul li {
  width: auto;
}
ul#superfish-grow3menu li.sf-depth-1:first-child > ul li ul {
  background: #fff;
}
ul#superfish-grow3menu li.sf-depth-1:not(:first-child) > ul {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
      -ms-flex-pack: end;
          justify-content: flex-end;
  padding-right: 5rem !important;
}
@media (min-width: 87.5rem) {
  ul#superfish-grow3menu li.sf-depth-1:not(:first-child) > ul {
    padding-right: calc(-83.4375rem + 100vw) !important;
  }
}
@media (min-width: 100rem) {
  ul#superfish-grow3menu li.sf-depth-1:not(:first-child) > ul {
    padding-right: calc((-88.4375rem + 100vw) / 1.25) !important;
  }
}
ul#superfish-grow3menu li > ul li.sf-depth-2.sf-no-children {
  margin-left: 2.5rem;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
}
ul#superfish-grow3menu li.sf-depth-1:first-child > ul li.sf-depth-2.sf-no-children {
  margin-top: 0;
}
ul#superfish-grow3menu li.sf-depth-1:first-child > ul li.sf-depth-2.sf-no-children:last-of-type {
  margin-top: 2.25rem;
}
ul#superfish-grow3menu li.sf-depth-1 > ul::after {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 10;
  content: " ";
  display: block;
  background: rgba(0, 0, 0, 0.07);
  height: 1px;
}
ul#superfish-grow3menu > li:first-child > ul {
  min-height: 530px;
  overflow: hidden;
}
ul#superfish-grow3menu > li > ul {
  background: #fff;
  top: 66px;
  width: 100% !important;
  z-index: 100;
  border-bottom: 1px solid silver;
  padding-top: 1rem;
  padding-bottom: 2rem;
  -webkit-box-shadow: 0 12px 9px 0 rgba(0, 0, 0, 0.5);
          box-shadow: 0 12px 9px 0 rgba(0, 0, 0, 0.5);
}
ul#superfish-grow3menu li.sf-depth-2 {
  max-width: 400px;
}
ul#superfish-grow3menu li.sf-depth-2.menuparent:first-child::after {
  content: " ";
  position: absolute;
  width: 1px;
  height: 400px;
  top: 16px;
  -webkit-transform: translateX(400px);
       -o-transform: translateX(400px);
          transform: translateX(400px);
  background: rgba(0, 0, 0, 0.07);
  z-index: 9999;
}
@media (min-width: 81.25rem) {
  ul#superfish-grow3menu li.sf-depth-2.menuparent:nth-child(2)::after {
    content: " ";
    position: absolute;
    width: 1px;
    height: 400px;
    top: 16px;
    -webkit-transform: translateX(930px);
         -o-transform: translateX(930px);
            transform: translateX(930px);
    background: rgba(0, 0, 0, 0.07);
    z-index: 9999;
  }
}
@media (min-width: 87.5rem) {
  ul#superfish-grow3menu li.sf-depth-2.menuparent:nth-child(2)::after {
    -webkit-transform: translateX(960px);
         -o-transform: translateX(960px);
            transform: translateX(960px);
  }
}
ul#superfish-grow3menu a.sf-depth-2.menuparent::after {
  content: "";
  width: 7px;
  height: 13px;
  display: inline-block;
  background: transparent no-repeat url("/themes/custom/bootstrap4grow/images/menu-arrow-right.png") 0 0;
  margin-left: 0.625rem;
  -webkit-transform: scale(0.8);
  -o-transform: scale(0.8);
     transform: scale(0.8);
}
ul#superfish-grow3menu a.sf-depth-2.menuparent {
  color: #000;
  pointer-events: none;
  font-weight: 600;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
ul#superfish-grow3menu > li:first-child > ul li.sf-depth-2.sf-no-children {
  float: none;
  padding-left: 1.875rem;
  margin-left: 0;
  margin-bottom: 0.625rem;
}
@media (min-width: 87.5rem) {
  ul#superfish-grow3menu > li:first-child > ul li.sf-depth-2.sf-no-children {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }
}
@media (min-width: 88.75rem) {
  ul#superfish-grow3menu > li:first-child > ul li.sf-depth-2.sf-no-children {
    padding-left: 0;
    padding-right: 0;
  }
}
ul#superfish-grow3menu > li:first-child > ul li.sf-depth-2.sf-no-children.n-menu-rcol {
  position: absolute;
}
ul#superfish-grow3menu > li > ul > li.sf-depth-2 > ul {
  top: 16px;
  width: 100% !important;
  min-height: 500px;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  -webkit-box-shadow: none;
          box-shadow: none;
}
ul#superfish-grow3menu .n-menu-rcol {
  position: absolute;
  top: 0;
}
ul#superfish-grow3menu .moneybox {
  width: 214px;
  height: 50px;
  margin-top: 0;
  background: transparent url("/themes/custom/bootstrap4grow/images/swinka5final.png") no-repeat 0 center;
  -webkit-background-size: contain;
          background-size: contain;
  text-indent: -9990px;
  pointer-events: all;
}
@media (min-width: 87.5rem) {
  ul#superfish-grow3menu .moneybox {
    width: 312px;
    height: 73px;
    margin-top: 1rem;
  }
}
ul#superfish-grow3menu .menu-link-promocje {
  width: 214px;
  height: 50px;
  margin-top: 0;
  background: transparent url("/themes/custom/bootstrap4grow/images/ikona_voucher.png") no-repeat 0 center;
  -webkit-background-size: contain;
          background-size: contain;
  text-indent: -9990px;
  pointer-events: all;
}
@media (min-width: 87.5rem) {
  ul#superfish-grow3menu .menu-link-promocje {
    width: 312px;
    height: 73px;
    margin-top: 1rem;
  }
}
ul#superfish-grow3menu .kalendarz {
  margin-top: 1.875rem;
  width: 214px;
  height: 50px;
  background: transparent url("/themes/custom/bootstrap4grow/images/kalendarz.png") no-repeat 0 0;
  -webkit-background-size: contain;
          background-size: contain;
  text-indent: -9990px;
  pointer-events: all;
}
@media (min-width: 87.5rem) {
  ul#superfish-grow3menu .kalendarz {
    width: 312px;
    height: 73px;
  }
}
ul#superfish-grow3menu > li > ul a {
  padding: 0;
  line-height: 1.1;
  padding: 0.25rem 0;
  font-size: 0.9375rem;
}
ul#superfish-grow3menu li.sf-depth-3 a {
  max-width: 500px;
  padding: 0.5rem 0;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  text-decoration: none;
}
ul#superfish-grow3menu a.sf-depth-3 {
  padding-bottom: 0.5rem;
  padding-top: 0.5rem;
}
@media (min-width: 65.625rem) {
  ul#superfish-grow3menu.sf-menu span.n-menu-red {
    margin-left: 0;
    padding: 0;
  }
}
ul#superfish-grow3menu li.sf-depth-2.menuparent {
  max-width: 1415px;
  padding-left: 1.875rem;
  padding-right: 1.875rem;
  margin: 0 auto;
  width: auto !important;
  float: none;
  font-size: 0.9375rem;
  line-height: 1.125rem;
  position: static;
}
@media (min-width: 87.5rem) {
  ul#superfish-grow3menu li.sf-depth-2.menuparent {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }
}
@media (min-width: 88.75rem) {
  ul#superfish-grow3menu li.sf-depth-2.menuparent {
    padding-left: 0;
    padding-right: 0;
  }
}

@media (min-width: 65.625rem) {
  .n-menu-search-wrapper-li input {
    position: relative;
    color: #000;
    padding: 0.625rem;
    border: #ffab1a 1px solid;
    border-radius: 5px;
    margin-left: 1.75rem;
    margin-top: 0.5rem;
  }
}
@media (min-width: 87.5rem) {
  .n-menu-search-wrapper-li input {
    margin-left: 0.5rem;
  }
}
@media (min-width: 88.75rem) {
  .n-menu-search-wrapper-li input {
    margin-left: 0;
  }
}

@media (min-width: 62rem) {
  ul.sf-menu span.n-menu-red {
    margin-left: 0.5rem;
  }
}
.n-menu-search-results-wrapper {
  position: absolute;
  top: 1em;
  z-index: 497;
}

ul.sf-menu ul li.n-menu-search-wrapper-li {
  position: static;
  max-width: 400px;
  padding-top: 0.625rem;
  padding-bottom: 0.9375rem;
  float: none;
}

ul.sf-menu ul.n-menu-search-results {
  position: relative;
  top: 0;
  width: 100% !important;
}

.container.npx-gray-bg {
  background: #f4f4f4;
  padding: 1.5625rem 0.9375rem;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  color: #333;
}

#cboxOverlay {
  background: #006bd9;
  opacity: 0.85 !important;
}

#cboxClose {
  position: absolute;
  bottom: auto;
  top: 5px;
  right: 5px;
  background: url("../images/close.png") no-repeat 0 0;
  width: 30px;
  height: 30px;
  text-indent: -9999px;
}
#cboxClose:hover {
  background-position: 0;
}

#cboxWrapper {
  border-radius: 0;
}

#cboxContent {
  padding: 4.375rem 0.3125rem 0.3125rem;
  border-radius: 0;
}
@media (min-width: 36rem) {
  #cboxContent {
    padding: 4.375rem 1.875rem 1.875rem;
  }
}
@media (min-width: 62rem) {
  #cboxContent {
    padding: 4.375rem 3.75rem 1.875rem;
  }
}

.red-text, .npx-red-text {
  color: #B3002F;
  font-weight: bold;
}

.site-footer {
  background: -webkit-gradient(linear, left top, right top, color-stop(0, #00366c), to(#0060c1));
  background: -webkit-linear-gradient(left, #00366c 0, #0060c1 100%);
  background: -o-linear-gradient(left, #00366c 0, #0060c1 100%);
  background: linear-gradient(to right, #00366c 0, #0060c1 100%);
  padding: 2.8125rem 0;
}

.region-footer-first {
  padding: 2.1875rem 4.375rem;
}

#block-bootstrap4grow-stopkadanekontaktowe {
  max-width: 1068px;
  margin-bottom: 3.125rem;
  margin-top: 3.125rem;
}
#block-bootstrap4grow-stopkadanekontaktowe h3 {
  font-size: 1.25rem;
  margin: 0;
  color: #fff;
}
#block-bootstrap4grow-stopkadanekontaktowe .contextual-links a {
  color: #333;
}
#block-bootstrap4grow-stopkadanekontaktowe a {
  color: #fff;
}
#block-bootstrap4grow-stopkadanekontaktowe a.n-facebook, #block-bootstrap4grow-stopkadanekontaktowe a.n-linkedin {
  position: relative;
  padding-left: 1.25rem;
}
#block-bootstrap4grow-stopkadanekontaktowe a.n-phone, #block-bootstrap4grow-stopkadanekontaktowe a.n-email {
  position: relative;
  padding-left: 1.25rem;
}
#block-bootstrap4grow-stopkadanekontaktowe a.n-phone::before, #block-bootstrap4grow-stopkadanekontaktowe a.n-email::before {
  content: "";
  position: absolute;
  top: 5px;
  width: 12px;
  height: 12px;
  margin-left: -1.25rem;
}
#block-bootstrap4grow-stopkadanekontaktowe a.n-phone::before {
  background: url("../images/Icon_footer_phone.png") no-repeat;
  -webkit-background-size: cover;
          background-size: cover;
}
#block-bootstrap4grow-stopkadanekontaktowe a.n-email::before {
  background: url("../images/Icon_footer_email.png") no-repeat;
  -webkit-background-size: contain;
          background-size: contain;
  background-position-y: 70%;
}
#block-bootstrap4grow-stopkadanekontaktowe a.n-facebook::before {
  content: "";
  position: absolute;
  top: 4px;
  width: 12px;
  height: 12px;
  margin-left: -1.25rem;
  background: url("../images/Icon_footer_facebook.png") no-repeat;
  -webkit-background-size: contain;
          background-size: contain;
}
#block-bootstrap4grow-stopkadanekontaktowe a.n-linkedin::before {
  content: "";
  position: absolute;
  top: 4px;
  width: 12px;
  height: 12px;
  margin-left: -1.25rem;
  background: url("../images/Icon_footer_linkedin.png") no-repeat;
  -webkit-background-size: contain;
          background-size: contain;
}
#block-bootstrap4grow-stopkadanekontaktowe .n-col {
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
}
@media (min-width: 62rem) {
  #block-bootstrap4grow-stopkadanekontaktowe .n-col {
    border-right: #6db4fa 2px solid;
  }
}
#block-bootstrap4grow-stopkadanekontaktowe .n-col:last-child {
  border: 0;
}

#block-bootstrap4grow-npxmailcontactblock {
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
  max-width: 100vw;
  background: #f3f3f5;
  padding: 2.5rem 0;
}
#block-bootstrap4grow-npxmailcontactblock .npx-mailcontact-right-info-n-img img {
  margin: 0 auto;
}

.npx-mailcontact-header {
  max-width: 1415px;
}
.npx-mailcontact-header h2 {
  font-size: 1.5rem;
}
@media (min-width: 62rem) {
  .npx-mailcontact-header h2 {
    font-size: 2rem;
  }
}
.npx-mailcontact-n-col-name {
  padding-right: 0;
  padding-left: 0;
}
@media (min-width: 36rem) {
  .npx-mailcontact-n-col-name {
    padding-right: 0.3125rem;
  }
}
.npx-mailcontact-n-col-name input {
  width: 100%;
  background: #fff url("../images/user_icon.svg") left center no-repeat;
  background-position-x: left;
  background-position-x: 8px;
}
.npx-mailcontact-n-col-email {
  padding-left: 0;
  padding-right: 0;
}
@media (min-width: 36rem) {
  .npx-mailcontact-n-col-email {
    padding-left: 0.3125rem;
  }
}
.npx-mailcontact-n-col-email input {
  width: 100%;
  background: #fff url("../images/icon_email.png") left center no-repeat;
  background-position-x: left;
  background-position-x: 8px;
  padding-left: 1.875rem;
}
.npx-mailcontact-form-wrapper-over .toast-wrapper, .npx-mailcontact-form-wrapper-over .messages {
  display: none !important;
}
.npx-mailcontact-form-wrapper {
  max-width: 1415px;
}
.npx-mailcontact-form-wrapper .js-form-type-checkbox input {
  display: none;
}
.npx-mailcontact-form-wrapper .form-textarea-wrapper {
  width: 100%;
}
.npx-mailcontact-form-wrapper label {
  display: block;
}
.npx-mailcontact-form-wrapper .field-suffix {
  display: none;
}
.npx-mailcontact-form-wrapper .error + div .field-suffix {
  display: block;
}
.npx-mailcontact-form-wrapper input.form-text.error, .npx-mailcontact-form-wrapper input.form-email.error,
.npx-mailcontact-form-wrapper textarea.error, .npx-mailcontact-form-wrapper input.form-checkbox.error {
  outline: #ff0000 1px solid;
}
.npx-mailcontact-form-wrapper input.form-checkbox.error + label {
  color: #fc5353;
}
.npx-mailcontact-form-wrapper div.form-textarea-wrapper + .field-suffix,
.npx-mailcontact-form-wrapper input.form-text + .field-suffix,
.npx-mailcontact-form-wrapper input.form-email + .field-suffix {
  display: none;
}
.npx-mailcontact-form-wrapper input.form-text.error + .field-suffix,
.npx-mailcontact-form-wrapper input.form-email.error + .field-suffix {
  display: block;
}
.npx-mailcontact-form-wrapper .js-form-type-checkbox label {
  padding: 0 0 0 1.875rem;
  background-color: transparent;
  background-image: url("../images/checkbox-off.png");
  background-repeat: no-repeat;
  background-position: 0px 2px;
  display: block;
  margin: 0;
  line-height: 1.25rem;
  font-size: 0.75rem;
}
.npx-mailcontact-form-wrapper .js-form-type-checkbox input:checked + label {
  background-image: url("../images/checkbox-on.png");
}
.npx-mailcontact-form-wrapper .required-info {
  font-size: 0.75rem;
  padding-left: calc(var(--bs-gutter-x) * 0.5);
}
.npx-mailcontact-form-wrapper .button.form-submit {
  margin: 0.625rem 0;
  padding: 0.75rem 1.875rem;
  width: auto;
}
@media (min-width: 36rem) {
  .npx-mailcontact-form-wrapper .button.form-submit {
    margin: 0.75rem 0 0 0.625rem;
  }
}
.npx-mailcontact-right-info-n-name {
  font-size: 1rem;
}
.npx-mailcontact-right-info-n-position {
  font-size: 0.875rem;
}
.npx-mailcontact-right-info-n-phone a {
  font-size: 1.375rem;
  color: #000;
}
.npx-mailcontact-right-info-n-img {
  margin-right: 0.625rem;
}
@media (min-width: 36rem) {
  .npx-mailcontact-right-info-n-img {
    margin-right: 1.25rem;
  }
}
.npx-mailcontact-right-info-n-img img {
  border: var(--secondary) 1px solid;
  border-radius: 50px;
  max-width: 80px;
  max-height: 80px;
}
.npx-mailcontact-right-info-n-row h3 {
  font-size: 1rem;
}
@media (min-width: 62rem) {
  .npx-mailcontact-right-info-n-row h3, .npx-mailcontact-right-info-n-row > p {
    text-align: left;
  }
}
.npx-mailcontact-right-info-n-row:first-child:after {
  margin-left: auto;
  margin-right: auto;
}
@media (min-width: 62rem) {
  .npx-mailcontact-right-info-n-row:first-child:after {
    margin-left: 6.25rem;
  }
}
.npx-mailcontact-left-info-n-row {
  margin-bottom: 3.5rem;
}
.npx-mailcontact-left-info-n-txt {
  font-size: 0.9375rem;
  line-height: 1.2;
}
.npx-mailcontact-left-info-n-icon {
  width: 85px;
  -webkit-flex-shrink: 0;
      -ms-flex-negative: 0;
          flex-shrink: 0;
}
.npx-mailcontact-middle-col .row {
  padding: 0 1.875rem;
}
.npx-mailcontact-middle-col .npx-mailcontact-middle-col-row-3 {
  padding: 0 calc(var(--bs-gutter-x) * 0.5 + 1.875rem);
}
.npx-mailcontact-middle-col .form-item-accept {
  margin: 0;
  margin-top: 0.625rem;
  padding-left: 0;
}
@media (min-width: 36rem) {
  .npx-mailcontact-middle-col .form-item-accept {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 0;
        -ms-flex: 1 0 0px;
            flex: 1 0 0;
  }
}
.npx-mailcontact-middle-col .form-item-message {
  width: 100%;
  margin-right: 0;
  padding: 0 calc(var(--bs-gutter-x) * 0.5);
}
.npx-mailcontact-middle-col .form-item-message .field-suffix {
  display: none;
}
.npx-mailcontact-middle-col textarea {
  width: 100%;
}
.npx-mailcontact-left-col {
  padding-left: 0;
}
.npx-mailcontact-right-col {
  margin-top: 2.5rem;
  text-align: center;
  padding-left: 1.875rem;
}
@media (min-width: 62rem) {
  .npx-mailcontact-right-col {
    border-left: #d4d8db 1px solid;
    padding-left: 0.9375rem;
  }
}
@media (min-width: 68.8125rem) {
  .npx-mailcontact-right-col {
    padding-left: 1.875rem;
  }
}
.npx-mailcontact-right-info-n-row:first-child:after {
  content: "";
  display: block;
  width: 215px;
  height: 1px;
  background: #ccc;
  margin-bottom: 1.25rem;
  margin-top: 1.25rem;
}
.npx-mailcontact-right-info {
  max-width: 400px;
}
@media (min-width: 62rem) {
  .npx-mailcontact-right-info {
    margin-top: -3.4375rem;
  }
}
.npx-mailcontact-left-info-h3 {
  font-size: 1.125rem;
  margin: 0 0 0.25rem 0;
}
.npx-mailcontact-right-info-h3 {
  font-size: 1.125rem;
  margin: 0 0 0.25rem 0;
}
.npx-mailcontact-thx-header {
  font-size: 1.75rem;
}

.npx-freshmail-block-footer-background {
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
  background: #ecedef;
}

.npx-freshmail-block-footer-wrapper {
  max-width: 1200px;
  color: #000;
}
.npx-freshmail-block-footer-wrapper .npx-left-column {
  padding-right: 2.5rem;
}
.npx-freshmail-block-footer-wrapper .npx-freshmail-accept + label a {
  color: #000;
  text-decoration: underline;
  cursor: pointer;
  font-weight: 400;
}
.npx-freshmail-block-footer-wrapper input.error {
  border-color: red;
}
.npx-freshmail-block-footer-wrapper input.error + label a, .npx-freshmail-block-footer-wrapper input.error + label {
  color: red;
}
.npx-freshmail-block-footer-wrapper .npx-right-column .js-form-type-checkbox {
  margin-bottom: 0;
  margin-top: 0;
}
.npx-freshmail-block-footer-wrapper .npx-right-column .js-form-type-checkbox label {
  padding: 0 0 0 1.875rem;
  background-color: transparent;
  background-image: url(../images/checkbox-off.png);
  background-repeat: no-repeat;
  background-position: 0 2px;
  display: block;
  margin: 0;
  line-height: 1.5rem;
  font-size: 0.875rem;
  font-weight: 400;
}
.npx-freshmail-block-footer-wrapper .npx-right-column .js-form-type-checkbox input {
  display: none;
}
.npx-freshmail-block-footer-wrapper .npx-right-column .js-form-type-checkbox input:checked + label {
  background-image: url(../images/checkbox-on-blue.png);
}
.npx-freshmail-block-footer-wrapper .npx-right-column form fieldset.npx-freshmail-list-id {
  margin-bottom: 0;
}
.npx-freshmail-block-footer-wrapper .npx-right-column form .npx-input-fields-wrapper {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
}
.npx-freshmail-block-footer-wrapper .npx-right-column form .npx-input-fields-wrapper label {
  font-size: 0.875rem;
  font-weight: 400;
}
.npx-freshmail-block-footer-wrapper .npx-right-column form .npx-input-fields-wrapper input {
  max-width: 200px;
}
.npx-freshmail-block-footer-wrapper .npx-right-column form .form-type-email, .npx-freshmail-block-footer-wrapper .npx-right-column form .form-type-textfield {
  max-width: 200px;
  margin: 0.625rem 1.25rem 0.625rem 0;
}
.npx-freshmail-block-footer-wrapper .npx-right-column form .form-submit, .npx-freshmail-block-footer-wrapper .npx-right-column form #edit-submit {
  margin: 0.625rem auto 0;
}
.npx-freshmail-block-footer-wrapper .npx-right-column form .n-wrapped-btn {
  position: relative;
}
.npx-freshmail-block-footer-wrapper .npx-right-column form .n-wrapped-btn input {
  padding-left: 5rem;
  max-width: 300px;
  height: auto;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  text-align: left;
  font-weight: 400;
  line-height: 0.875rem;
  font-size: 0.75rem;
  padding-right: 1.5rem;
  width: auto;
}
.npx-freshmail-block-footer-wrapper .npx-right-column form .n-wrapped-btn span {
  position: absolute;
  top: calc(50% - 18px);
  left: 10px;
  color: #fff;
  font-size: 1.75rem;
  font-weight: 700;
  pointer-events: none;
  z-index: 10;
}
.npx-freshmail-block-footer-wrapper .nxp-top-container {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
}
.npx-freshmail-block-footer-wrapper .nxp-top-container h4 {
  font-size: 1.8rem;
  color: #06c;
  margin: 0.25rem 0;
  font-weight: 700;
  text-align: center;
}
@media (min-width: 48rem) {
  .npx-freshmail-block-footer-wrapper .nxp-top-container h4 {
    text-align: left;
  }
}
.npx-freshmail-block-footer-wrapper .npx-freshmail-list-id .fieldset-legend {
  margin-bottom: 0.625rem;
  display: inline-block;
  font-size: 1rem;
}
.npx-freshmail-block-footer-wrapper .nxp-top-text-container {
  line-height: 1.4rem;
  color: #505050;
}
.npx-freshmail-block-footer-wrapper .nxp-top-text-container p {
  margin: 0;
  font-size: 1.1rem;
}
.npx-freshmail-block-footer-wrapper .npx-msg {
  display: none;
}

.npx-freshmail-block-footer-wrapper.npx-freshmail-form-sent .npx-msg {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
  width: 100%;
  min-height: 300px;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  font-size: 1.5rem;
  background: url("../img/Icon_newsletter_stopka_TYP.png") center 60px no-repeat;
  color: #909090;
}
.npx-freshmail-block-footer-wrapper.npx-freshmail-form-sent .npx-msg h3 {
  margin-bottom: 0;
  font-size: 1.8rem;
  font-weight: normal;
  font-style: italic;
  color: #909090;
}
.npx-freshmail-block-footer-wrapper.npx-freshmail-form-sent .npx-msg p {
  margin-top: 0;
  font-size: 1.2rem;
}
.npx-freshmail-block-footer-wrapper.npx-freshmail-form-sent .nxp-columns-container {
  display: none !important;
}

.readmore-js-link, .readless-js-link {
  margin: 0;
  padding: 0.1875rem 0;
  border-radius: 5px;
  background: #f3f3f5;
  color: #4c4c4c;
  text-align: center;
  text-decoration: none;
  margin-bottom: 0.5rem;
}

.readless-js-link {
  display: none;
}

.readmore-js-link {
  display: block;
}

.readmore-item {
  height: 100px;
  overflow: hidden;
}
.readmore-item.readmore-big {
  height: 250px;
}

.readmore-open .readless-js-link {
  display: block;
}
.readmore-open .readmore-js-link {
  display: none;
}
.readmore-open .readmore-item {
  height: auto;
}

.readmore-js:not(.readmore-open) .readmore-item {
  position: relative;
}
.readmore-js:not(.readmore-open) .readmore-item::after {
  content: "";
  position: absolute;
  bottom: 0;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background: -webkit-linear-gradient(rgba(255, 255, 255, 0) 20%, rgba(255, 255, 255, 0.9) 95%);
  background-image: -webkit-linear-gradient(rgba(255, 255, 255, 0) 20%, rgba(255, 255, 255, 0.9) 95%);
  background-image: -webkit-gradient(linear, left top, left bottom, color-stop(20%, rgba(255, 255, 255, 0)), color-stop(95%, rgba(255, 255, 255, 0.9)));
  background-image: linear-gradient(rgba(255, 255, 255, 0) 20%, rgba(255, 255, 255, 0.9) 95%);
  background-image: -o-linear-gradient(rgba(255, 255, 255, 0) 20%, rgba(255, 255, 255, 0.9) 95%);
  pointer-events: none;
}

.slick-slider ul.slick-dots {
  margin: 0 0 0;
  padding: 0;
  position: relative;
  right: auto;
  left: auto;
  bottom: auto;
  top: auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
}
.slick-slider ul.slick-dots li {
  margin: 0;
  padding: 0;
  list-style-type: none;
  list-style-image: none;
}
.slick-slider ul.slick-dots li button {
  display: block;
  border: none;
  width: 16px;
  height: 16px;
  padding: 0;
  margin: 0 0.25rem;
  background: #d1d9dc;
  border-radius: 50px;
  text-align: left;
  text-indent: -9990px;
}
.slick-slider ul.slick-dots li button:hover {
  color: #98a0a3;
}
.slick-slider ul.slick-dots li.slick-active button {
  background-color: #fecc09;
}
.slick-slider ul.slick-dots li.slick-active button:hover {
  background-color: #fecc09;
}

button.slick-arrow {
  margin: 0;
  padding: 0;
  position: relative;
  display: block;
  opacity: 1;
  width: 24px;
  height: 32px;
  background-position: 0 0;
  background-repeat: no-repeat;
  background-image: url("../images/arrows.png");
  text-align: left;
  text-indent: -9990px;
  border: none;
  background-color: transparent;
}
button.slick-arrow.slick-prev {
  background-position: 0 0;
}
button.slick-arrow.slick-next {
  background-position: -24px 0;
}
button.slick-arrow.slick-prev:hover {
  background-position: 0 -32px;
  background-image: url("../images/arrows.png");
}
button.slick-arrow.slick-next:hover {
  background-position: -24px -32px;
  background-image: url("../images/arrows.png");
}

.mylivechat_inline.mylivechat-mobile-docked {
  max-width: calc(100% - 30px);
}

.mylivechat_buttonround img {
  position: absolute !important;
  width: 80px !important;
  height: 80px !important;
  left: 0 !important;
  top: 0 !important;
  border-radius: 40px;
}

.mylivechat_buttonround_tooltip {
  width: 200px !important;
  top: 20px !important;
  right: 110px !important;
  white-space: normal !important;
  padding: 0.5rem !important;
  line-height: 1rem !important;
  display: block !important;
}
@media (min-width: 36rem) {
  .mylivechat_buttonround_tooltip {
    width: 360px !important;
  }
}

.mylivechat_buttonround {
  width: 80px !important;
  height: 80px !important;
  top: -20px !important;
  left: auto !important;
  right: 5px !important;
  background: #fff !important;
}

.n-order-summary-price {
  font-size: 1.25rem;
}

.opinia .group-footer {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
      -ms-flex-pack: end;
          justify-content: flex-end;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  background-image: url("../images/opinia.png");
  background-position: right top;
  background-repeat: no-repeat;
  padding-top: 1.875rem;
}
.opinia .group-footer img {
  border-radius: 50%;
  -webkit-box-shadow: 1px 1px 6px 0 #a1a1a1;
          box-shadow: 1px 1px 6px 0 #a1a1a1;
}
.opinia .field--name-field-linkedin-link {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-self: flex-end;
      -ms-flex-item-align: end;
          align-self: flex-end;
}
.opinia .field--name-field-linkedin-link a {
  position: relative;
  text-indent: -99999px;
  text-align: left;
  display: block;
  width: 46px;
  height: 11px;
  margin-top: 0.3125rem;
  -webkit-background-size: contain;
          background-size: contain;
  background-repeat: no-repeat;
  -webkit-align-self: end;
      -ms-flex-item-align: end;
          align-self: end;
}
.opinia .field--name-field-linkedin-link a::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("../images/linkedin-logo.png");
  -webkit-filter: grayscale(100%);
          filter: grayscale(100%);
}
.opinia .n-signature-wrapper {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-align-content: flex-end;
      -ms-flex-line-pack: end;
          align-content: flex-end;
  margin-right: 1.25rem;
}
.opinia .n-signature-wrapper p {
  margin: 0;
  text-align: right;
}
.opinia .field--name-field-image {
  margin-left: 0;
  padding-right: 0.375rem;
}
.opinia .field--name-field-zajawka p {
  font-size: 0.75rem;
  font-weight: bold;
  line-height: 1.1875rem;
}

.right-wrapper-column-inner span.h3 {
  margin: 5rem 0 2.9rem 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}
@media (min-width: 62rem) {
  .right-wrapper-column-inner {
    max-width: 80%;
  }
}

@media (min-width: 62rem) {
  #edit-email-btn-container {
    max-width: 40%;
  }
}
#edit-email-btn-container .form-email {
  max-width: 100%;
}

.npx-voucher-wrapper {
  position: fixed;
  top: 100px;
  right: 0;
  display: none;
  z-index: 999;
}

a.npx-close-voucher-block, a.npx-close-voucher-second-block {
  top: 13px;
}

.npx-close-voucher-block, .npx-close-voucher-second-block {
  position: absolute;
  right: 0;
  top: 0;
  width: 32px;
  height: 32px;
  opacity: 0.7;
  padding: 0.625rem;
}
.npx-close-voucher-block:before, .npx-close-voucher-block:after, .npx-close-voucher-second-block:before, .npx-close-voucher-second-block:after {
  position: absolute;
  left: 15px;
  content: " ";
  height: 16px;
  width: 2px;
  background-color: #333;
  -webkit-box-shadow: 0 0 10px 2px #fff;
          box-shadow: 0 0 10px 2px #fff;
}
.npx-close-voucher-block:before, .npx-close-voucher-second-block:before {
  -webkit-transform: rotate(45deg);
       -o-transform: rotate(45deg);
          transform: rotate(45deg);
}
.npx-close-voucher-block:after, .npx-close-voucher-second-block:after {
  -webkit-transform: rotate(-45deg);
       -o-transform: rotate(-45deg);
          transform: rotate(-45deg);
}

.block-voucher-second-block {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  display: none;
  z-index: 999999;
}

.npx-voucher-second-wrapper {
  position: fixed;
  top: 50px;
  left: 50%;
  -webkit-transform: translateX(-50%);
       -o-transform: translateX(-50%);
          transform: translateX(-50%);
}
.npx-voucher-second-wrapper img {
  max-height: calc(100vh - 100px);
  width: auto;
}

html, body {
  overflow-x: hidden;
}

em {
  font-weight: 600;
}

body ol, body ul {
  padding-left: 1rem;
}

.text-red {
  color: #dc3545;
}

ul {
  list-style-image: url("../images/li_checkmark.png");
}
ul li {
  margin-bottom: 0.5rem;
}
ul ul {
  list-style-image: url("../images/li_full.png");
  margin-top: 0.5rem;
}

img[data-align=right] {
  float: right;
  padding: 0.3125rem 0.625rem;
}

img.align-left {
  float: left;
  margin: 1rem 2rem 0.5rem 0;
}

.toast-wrapper {
  display: none !important;
}

textarea {
  border-color: #d4d8db;
  border-width: 1px;
  border-radius: 0;
  background-color: #fff;
  padding: 0.75rem;
  border-style: solid;
  line-height: 1.75rem;
  margin-top: 0;
  margin-bottom: 0;
}

legend {
  font-weight: 700;
}

.js-form-item {
  margin-top: 1rem;
  margin-bottom: 1rem;
  position: relative;
}

.form-type-textfield input, input.form-email, .form-item-subscriber-name input {
  border-color: #d4d8db;
  border-width: 1px;
  border-radius: 0;
  background-color: #fff;
  padding: 0 0 0 1.5rem;
  border-style: solid;
  line-height: 3rem;
  height: 50px;
  margin-top: 0;
  margin-bottom: 0;
}

label {
  margin-bottom: 0.5rem;
}

input[type=radio], input[type=checkbox] {
  display: none;
}

input[type=radio]:checked + label, input[type=checkbox]:checked + label {
  background-image: url(../images/checkbox-on.png);
}

.js-form-type-checkbox {
  padding-left: 0;
}

.js-form-type-radio label, .js-form-type-checkbox label {
  padding: 0 0 0 1.875rem;
  background-color: transparent;
  background-image: url(../images/checkbox-off.png);
  background-repeat: no-repeat;
  background-position: 0 center;
  display: block;
  margin: 0;
  line-height: 1.25rem;
  font-size: 0.875rem;
}

a.npx-form-button {
  text-decoration: none;
  text-transform: none !important;
}
/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
