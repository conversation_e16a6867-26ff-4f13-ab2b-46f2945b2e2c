{#
/**
 * @file
 * Theme override for a set of links.
 *
 * Available variables:
 * - attributes: Attributes for the UL containing the list of links.
 * - links: Links to be output.
 *   Each link will have the following elements:
 *   - title: The link text.
 *   - href: The link URL. If omitted, the 'title' is shown as a plain text
 *     item in the links list. If 'href' is supplied, the entire link is passed
 *     to l() as its $options parameter.
 *   - attributes: (optional) HTML attributes for the anchor, or for the <span>
 *     tag if no 'href' is supplied.
 *   - link_key: The link CSS class.
 * - heading: (optional) A heading to precede the links.
 *   - text: The heading text.
 *   - level: The heading level (e.g. 'h2', 'h3').
 *   - attributes: (optional) A keyed list of attributes for the heading.
 *   If the heading is a string, it will be used as the text of the heading and
 *   the level will default to 'h2'.
 *
 *   Headings should be used on navigation menus and any list of links that
 *   consistently appears on multiple pages. To make the heading invisible use
 *   the 'sr-only' CSS class. Do not use 'display:none', which
 *   removes it from screen readers and assistive technology. Headings allow
 *   screen reader and keyboard only users to navigate to or skip the links.
 *   See http://juicystudio.com/article/screen-readers-display-none.php and
 *   http://www.w3.org/TR/WCAG-TECHS/H42.html for more information.
 *
 * @see template_preprocess_links()
 */
#}
{% if links -%}
  {{ attach_library('bootstrap_barrio/links') }}

  {%- if heading -%}
    {%- if heading.level -%}
      <{{ heading.level }}{{ heading.attributes }}>{{ heading.text }}</{{ heading.level }}>
    {%- else -%}
      <h2{{ heading.attributes }}>{{ heading.text }}</h2>
    {%- endif -%}
  {%- endif -%}
  <ul{{ attributes }}  vocab="https://schema.org/" typeof="BreadcrumbList">
    {%- for key, item in links -%}
      <li{{ item.attributes.addClass(key|clean_class).addClass('d-inline', 'm-0', 'p-0', 'list-unstyled') }}  property="itemListElement" typeof="ListItem">
        {%- if item.link -%}
          <a property="item" typeof="WebPage" href="{{ item.link['#url'] }}">
            <span property="name">{{ item.link['#title'] }}</span></a>
          <meta property="position" content="{{ loop.index }}">
        {%- elseif item.text_attributes -%}
          <span property="name" {{ item.text_attributes }} >{{ item.text }}</span>
          <meta property="position" content="{{ loop.index }}">
        {%- else -%}
          <span property="name">
            {{ item.text }}
          </span>
          <meta property="position" content="{{ loop.index }}">
        {%- endif -%}
      </li>
    {%- endfor -%}
  </ul>
{%- endif %}
