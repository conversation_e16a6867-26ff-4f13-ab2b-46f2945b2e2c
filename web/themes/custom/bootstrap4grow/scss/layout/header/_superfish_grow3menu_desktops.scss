ul#superfish-grow3menu {
  a.menuparent:hover::after {
    -moz-transform: scaleY(1);
    -ms-transform: scaleY(1);
    -webkit-transform: scaleY(1);
    transform: scaleY(1);
  }
  li.sf-depth-1:not(:first-child) > ul li {
    width: auto;
  }
  li.sf-depth-1:first-child > ul li ul {
    background: #fff;
  }
  li.sf-depth-1:not(:first-child) > ul {
    display: flex;
    justify-content: flex-end;
    padding-right: 80px!important;
    @include media-breakpoint-up(ld) {
      padding-right: calc( -1415px + 80px + 100vw)!important;
    }
    @include media-breakpoint-up(xxl) {
      padding-right: calc((-1415px + 100vw)/1.25)!important;
    }
  }
  li > ul li.sf-depth-2.sf-no-children {
    margin-left: spacer(7);
    box-sizing: border-box;
  }
  li.sf-depth-1:first-child > ul li.sf-depth-2.sf-no-children {
    margin-top: 0;
    &:last-of-type {
      margin-top: 36px;
    }
  }
  li.sf-depth-1 > ul::after {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 10;
    content: " ";
    display: block;
    background: rgba(0,0,0,.07);
    height: 1px;
  }
  & > li:first-child > ul {
    min-height: 530px;
    overflow: hidden;
  }
  & > li > ul {
    background: #fff;
    top: 66px;
    width: 100% !important;
    z-index: 100;
    border-bottom: 1px solid silver;
    padding-top: spacer(4);
    padding-bottom: 32px;
    box-shadow: 0 12px 9px 0 rgba(0,0,0,.5);
  }
  li.sf-depth-2 {
    max-width: 400px;
    &.menuparent:first-child::after {
      content: " ";
      position: absolute;
      width: 1px;
      height: 400px;
      top: 16px;
      transform: translateX(400px);
      background: rgba(0,0,0,.07);
      z-index: 9999;
    }
    &.menuparent:nth-child(2)::after {
      @include media-breakpoint-up(xla) {
        content: " ";
        position: absolute;
        width: 1px;
        height: 400px;
        top: 16px;
        transform: translateX(930px);
        background: rgba(0,0,0,.07);
        z-index: 9999;
      }
      @include media-breakpoint-up(ld) {
        transform: translateX(960px);
      }
    }
  }
  & a.sf-depth-2.menuparent::after {
    content: "";
    width: 7px;
    height: 13px;
    display: inline-block;
    background: transparent no-repeat url("/themes/custom/bootstrap4grow/images/menu-arrow-right.png") 0 0;
    margin-left: spacer(2);
    -moz-transform: scale(0.8);
    -ms-transform: scale(0.8);
    -webkit-transform: scale(0.8);
    transform: scale(0.8);
  }
  a.sf-depth-2.menuparent {
    color: #000;
    pointer-events: none;
    font-weight: 600;
    padding-top: spacer(1);
    padding-bottom: spacer(1);
  }
  & > li:first-child > ul li.sf-depth-2.sf-no-children {
    float: none;
    padding-left: spacer(6);
    margin-left: 0;
    margin-bottom: spacer(2);
    @include media-breakpoint-up(ld) {
      padding-left: spacer(1);
      padding-right: spacer(1);
    }
    @include media-breakpoint-up(ldsuperfish,(ldsuperfish:1420px)) {
      padding-left: 0;
      padding-right: 0;
    }
    &.n-menu-rcol {
      position: absolute;
    }
  }
  & > li > ul > li.sf-depth-2 > ul {
    top: 16px;
    width: 100% !important;
    min-height: 500px;
    box-sizing: border-box;
    box-shadow: none;
  }
  .n-menu-rcol {
    position: absolute;
    top: 0;
  }
  .moneybox {
    width: 214px;
    height: 50px;
    margin-top: 0;
    background: transparent url("/themes/custom/bootstrap4grow/images/swinka5final.png") no-repeat 0 center;
    background-size: contain;
    text-indent: -9990px;
    pointer-events: all;
    @include media-breakpoint-up(ld) {
      width: 312px;
      height: 73px;
      margin-top: spacer(4);
    }
  }
  .menu-link-promocje {
    width: 214px;
    height: 50px;
    margin-top: 0;
    background: transparent url("/themes/custom/bootstrap4grow/images/ikona_voucher.png") no-repeat 0 center;
    background-size: contain;
    text-indent: -9990px;
    pointer-events: all;
    @include media-breakpoint-up(ld) {
      width: 312px;
      height: 73px;
      margin-top: spacer(4);
    }
  }
  .kalendarz {
    margin-top: spacer(6);
    width: 214px;
    height: 50px;
    background: transparent url("/themes/custom/bootstrap4grow/images/kalendarz.png") no-repeat 0 0;
    background-size: contain;
    text-indent: -9990px;
    pointer-events: all;
    @include media-breakpoint-up(ld) {
      width: 312px;
      height: 73px;
    }
  }
  & > li > ul {
    a {
      padding: 0;
      line-height: 1.1;
      padding: 4px 0;
      font-size: 15px;
    }
  }
  li.sf-depth-3 a {
    max-width: 500px;
    padding: spacer(1 0);
    box-sizing: border-box;
    text-decoration: none;
  }
  a.sf-depth-3 {
    padding-bottom: spacer(1);
    padding-top: spacer(1);
  }
  &.sf-menu span.n-menu-red {
    @include media-breakpoint-up(lgm) {
      margin-left: 0;
      padding: 0;
    }
  }
  li.sf-depth-2.menuparent {
    max-width: $max-width-container;
    padding-left: spacer(6);
    padding-right: spacer(6);
    margin: 0 auto;
    width: auto !important;
    float: none;
    font-size: 15px;
    line-height: 18px;
    position: static;
    @include media-breakpoint-up(ld) {
      padding-left: spacer(1);
      padding-right: spacer(1);
    }
    @include media-breakpoint-up(ldsuperfish,(ldsuperfish:1420px)) {
      padding-left: 0;
      padding-right: 0;
    }
  }
}
.n-menu-search-wrapper-li input {
  @include media-breakpoint-up(lgm) {
    position: relative;
    color: #000;
    padding: spacer(2);
    border: #ffab1a 1px solid;
    border-radius: 5px;
    margin-left: 28px;
    margin-top: spacer(1);
  }
  @include media-breakpoint-up(ld) {
    margin-left: spacer(1);
  }
  @include media-breakpoint-up(ldsuperfish,(ldsuperfish:1420px)) {
    margin-left: 0;
  }
}
@include media-breakpoint-up(lg) {
  ul.sf-menu span.n-menu-red {
    margin-left: spacer(1);
  }
}
.n-menu-search-results-wrapper {
  position: absolute;
  top: 1em;
  z-index: 497;
}
ul.sf-menu ul li.n-menu-search-wrapper-li {
  position: static;
  max-width: 400px;
  padding-top: 10px;
  padding-bottom: 15px;
  float: none;
}
ul.sf-menu ul.n-menu-search-results {
  position: relative;
  top: 0;
  width: 100% !important;
}
