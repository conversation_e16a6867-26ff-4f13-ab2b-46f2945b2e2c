// import files
@import "../bootstrap/scss/common/base";
@import "../bootstrap/scss/components/nav";

@import "layout/header/superfish_grow3menu";
@import "layout/header/superfish_grow3menu_mobile";
@import "layout/header/superfish_grow3menu_desktops";
@import "layout/global";
@import "layout/footer/site_footer";
@import "layout/footer/block_stopkadanekontaktowe";

@import "components/module_npx_mailcontact_block";
@import "components/module_npx_freshmail_block_footer";
@import "components/readmore";
@import "components/slick";
@import "components/mylivechat";
@import "pages/page/aftertrainingform";
@import "pages/npxtest";
@import "components/voucher";

//@import "components/module_npx_valentines_exit_popup_block"; //id ok

html, body {
  overflow-x: hidden;
}
em {
  font-weight: 600;
}

body ol, body ul {
  padding-left: 1rem;
}
.text-red {
  color: $red;
}
ul {
  list-style-image: url("../images/li_checkmark.png");
  li {
    margin-bottom: spacer(1);
  }
  ul {
    list-style-image: url("../images/li_full.png");
    margin-top: spacer(1);
  }
}

img[data-align="right"] {
  float: right;
  padding: spacer(5px 2);
}
img.align-left {
  float: left;
  margin: spacer(4 32px 1 0);
}
.toast-wrapper {
  display: none!important;
}

textarea {
  border-color: #d4d8db;
  border-width: 1px;
  -moz-border-radius: 0;
  -webkit-border-radius: 0;
  border-radius: 0;
  background-color: #fff;
  padding: 12px;
  border-style: solid;
  line-height: 28px;
  margin-top: 0;
  margin-bottom: 0;
}

legend {
  font-weight: 700;
}

.js-form-item {
  margin-top: spacer(4);
  margin-bottom: spacer(4);
  position: relative;
}

.form-type-textfield input, input.form-email, .form-item-subscriber-name input {
  border-color: #d4d8db;
  border-width: 1px;
  -moz-border-radius: 0;
  -webkit-border-radius: 0;
  border-radius: 0;
  background-color: #fff;
  padding: 0 0 0 24px;
  border-style: solid;
  line-height: 48px;
  height: 50px;
  margin-top: 0;
  margin-bottom: 0;
}

label {
  margin-bottom: spacer(1);
}
input[type="radio"], input[type="checkbox"] {
  display: none;
}
input[type="radio"]:checked + label, input[type="checkbox"]:checked + label {
  background-image: url(../images/checkbox-on.png);
}
.js-form-type-checkbox {
  padding-left: 0;
}
.js-form-type-radio label, .js-form-type-checkbox label {
  padding: 0 0 0 30px;
  background-color: transparent;
  background-image: url(../images/checkbox-off.png);
  background-repeat: no-repeat;
  background-position: 0 center;
  display: block;
  margin: 0;
  line-height: 20px;
  font-size: 14px;
}
a.npx-form-button {
  text-decoration: none;
  text-transform: none !important;
}
