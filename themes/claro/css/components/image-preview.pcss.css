/**
 * @file
 * Image preview component.
 */

:root {
  --color-pattern: var(--color-gray-200);
  --size-pattern-square: calc(7rem / 16);
}

/**
 * Image preview.
 */
.image-preview {
  line-height: 0;

  & img {
    background-image: linear-gradient(-45deg, var(--color-pattern) 25%, transparent 26%), linear-gradient(-45deg, var(--color-pattern) 25%, transparent 26%), linear-gradient(135deg, var(--color-pattern) 25%, transparent 26%), linear-gradient(135deg, var(--color-pattern) 25%, transparent 26%);
    background-position:
      0 0,
      var(--size-pattern-square) var(--size-pattern-square),
      var(--size-pattern-square) var(--size-pattern-square),
      0 0;
    background-size: calc(var(--size-pattern-square) * 2) calc(var(--size-pattern-square) * 2);
  }
}

.image-preview__img-wrapper {
  display: inline-block;
  max-width: 100%;
  background-color: var(--color-white);
  box-shadow: inset 0 0 var(--size-pattern-square) var(--color-pattern);
}

/**
 * Don't display file icon in image widgets.
 */
.image-widget .file.file {
  padding: 0;
  background: none;
}
