/*
 * DO NOT EDIT THIS FILE.
 * See the following change record for more information,
 * https://www.drupal.org/node/3084859
 * @preserve
 */

/**
 * @file
 * Styling for the Filter module.
 */

/**
 * Filter information under field.
 */

.text-full > .form-item {
  margin-bottom: 0;
}

.form-element--editor-format {
  vertical-align: top;
}

@media screen and (max-width: 37.5rem) {
  /* Leave editor's select auto-scaled. */
  .form-element.form-element--editor-format {
    width: auto;
  }
}

.filter-wrapper {
  margin-top: 0.5em; /* (8 / 16) */
  margin-bottom: 0.5em; /* (8 / 16) */
}

.filter-wrapper .form-item {
  margin: 0;
}

.filter-help {
  float: right; /* LTR */
  padding-top: 0.25em; /* (3 / 12), because font size is 12px */
  padding-bottom: 0.25em; /* (3 / 12) */
  font-size: 0.75em; /* (12 / 16), inherited font size is 16px */
}

[dir="rtl"] .filter-help {
  float: left;
}

/**
 * Compose tips.
 *
 * Wraps filter tips on page '/filter/tips[/name]'.
 */

.compose-tips__item {
  margin-top: 1.5em;
  margin-bottom: 1.5em;
}

/**
 * Filter guidelines.
 */

.filter-guidelines__item {
  margin-top: 0.5em; /* (6 / 12) */
  color: var(--input-fg-color--description);
  font-size: 0.75em; /* (12 / 16) */
}

.filter-guidelines p {
  margin-top: 0.3333em; /* (4 / 12) */
  margin-bottom: 0;
}

/**
 * Filter tips.
 *
 * Long is used on '/filter/tips[/name]', short is used for the filter tips
 * below a text format input.
 */

.filter-tips--long {
  margin-bottom: 1.5em;
}

.filter-tips__item,
.filter-tips--long p {
  margin-top: 0.75em;
  margin-bottom: 0.75em;
}

.filter-tips__item--short {
  margin-top: 0.3333em; /* (4 / 12) */
  margin-bottom: 0;
}
