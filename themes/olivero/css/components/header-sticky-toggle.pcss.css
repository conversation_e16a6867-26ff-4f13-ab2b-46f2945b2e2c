/**
 * @file
 * Sticky Header Toggle Button.
 *
 * This button shows on the left hand side of the header (in LTR layouts), and
 * toggles fixing the header to the top of the viewport.
 */

@import "../base/media-queries.pcss.css";

.sticky-header-toggle {
  display: none;

  @media (--nav) {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    justify-content: center;
    width: var(--content-left);
    height: var(--sp6);
    pointer-events: none;
    opacity: 0;
    border: 0;
    outline: 0;
    background-color: var(--color--primary-50);

    &:focus {
      cursor: pointer;
      pointer-events: auto;
      opacity: 1;
      outline: solid 2px var(--color--white);
      outline-offset: -4px;
    }
  }
}

body:not(.is-always-mobile-nav) .is-fixed .sticky-header-toggle {
  @media (--nav) {
    visibility: visible;
  }
}

body.is-always-mobile-nav .sticky-header-toggle {
  @media (--nav) {
    visibility: hidden;
  }
}

.sticky-header-toggle__icon {
  --icon-bar-height: 3px;
  --icon-bar-space: 7px;

  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: var(--sp2);
  height: calc((var(--icon-bar-height) * 3) + (var(--icon-bar-space) * 2));
  /* Height = 3 bars + 2 spaces */
  transition: opacity 0.2s;
  pointer-events: none;
  transform-style: preserve-3d;

  & > span {
    display: block;
    width: 100%;
    height: var(--icon-bar-height);
    transition: transform 0.2s;
    transform-origin: center;
    background-color: var(--color--white);
  }
}

.is-fixed .sticky-header-toggle {
  cursor: pointer;
  pointer-events: auto;
  opacity: 1;
}

[aria-checked="true"] .sticky-header-toggle__icon {
  & > span:nth-child(1) {
    transform: translateY(calc(var(--icon-bar-height) + var(--icon-bar-space))) rotate(-45deg);
  }

  & > span:nth-child(2) {
    opacity: 0;
  }

  & > span:nth-child(3) {
    transform: translateY(calc(0px - var(--icon-bar-height) - var(--icon-bar-space))) rotate(45deg);
  }
}
