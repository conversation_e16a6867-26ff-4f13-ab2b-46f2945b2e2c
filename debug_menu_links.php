<?php

use Drupal\Core\DrupalKernel;
use Symfony\Component\HttpFoundation\Request;
use Drupal\menu_link_content\Entity\MenuLinkContent;

// Bootstrap Drupal
$autoloader = require_once 'vendor/autoload.php';
$kernel = new DrupalKernel('prod', $autoloader);
$request = Request::createFromGlobals();
$response = $kernel->handle($request);
$kernel->boot();

// Find all menu links in grow3menu
$query = \Drupal::entityQuery('menu_link_content')
  ->condition('menu_name', 'grow3menu')
  ->accessCheck(FALSE);

$menu_link_ids = $query->execute();

echo "Found " . count($menu_link_ids) . " menu links in grow3menu\n\n";

foreach ($menu_link_ids as $menu_link_id) {
  $menu_link = MenuLinkContent::load($menu_link_id);
  
  if ($menu_link) {
    $title = $menu_link->getTitle();
    $uri = $menu_link->link->first()->uri;
    $link_options = $menu_link->link->first()->options;
    
    echo "=== LINK ID: " . $menu_link_id . " ===\n";
    echo "Title: " . $title . "\n";
    echo "URI: " . $uri . "\n";
    echo "Options: " . print_r($link_options, true) . "\n";
    
    // Check for specific titles that might be our promotion link
    if (stripos($title, 'promocje') !== false || 
        stripos($title, 'letnia') !== false || 
        stripos($title, 'kampania') !== false ||
        stripos($title, 'specjalne') !== false ||
        stripos($title, 'lato') !== false) {
      echo "*** POTENTIAL PROMOTION LINK FOUND ***\n";
    }
    
    echo "\n";
  }
}

echo "Script completed.\n";
