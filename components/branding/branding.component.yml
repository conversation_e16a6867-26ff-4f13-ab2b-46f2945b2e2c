# This is so your IDE knows about the syntax for fixes and autocomplete.
$schema: https://git.drupalcode.org/project/drupal/-/raw/HEAD/core/assets/schemas/v1/metadata.schema.json

# The human readable name.
name: Branding

# Status can be: "experimental", "stable", "deprecated", "obsolete".
status: stable

# Use this key to organize components together.
group: Navigation

# Schema for the props. We support www.json-schema.org. Learn more about the
# syntax there.
props:
  # Props are always an object with keys. Each key is a variable in your
  # component template.
  type: object

  properties:
    attributes:
      type: Drupal\Core\Template\Attribute
      title: Attributes
      description: Wrapper attributes.
    site_logo:
      type: string
      format: iri-reference
    url:
      type: string
      title: Branding url
      format: iri-reference
    site_name:
      type: string
      title: Site name
    site_slogan:
      type: string
      title: Site slogan
