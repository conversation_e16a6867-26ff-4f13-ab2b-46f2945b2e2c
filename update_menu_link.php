<?php

use <PERSON><PERSON>al\Core\DrupalKernel;
use Symfony\Component\HttpFoundation\Request;
use Drupal\menu_link_content\Entity\MenuLinkContent;

// Bootstrap Drupal
$autoloader = require_once 'vendor/autoload.php';
$kernel = new DrupalKernel('prod', $autoloader);
$request = Request::createFromGlobals();
$response = $kernel->handle($request);
$kernel->boot();

// Find menu link with class "menu-link-promocje" in grow3menu
$query = \Drupal::entityQuery('menu_link_content')
  ->condition('menu_name', 'grow3menu')
  ->accessCheck(FALSE);

$menu_link_ids = $query->execute();

echo "Found " . count($menu_link_ids) . " menu links in grow3menu\n";

foreach ($menu_link_ids as $menu_link_id) {
  $menu_link = MenuLinkContent::load($menu_link_id);
  
  if ($menu_link) {
    $title = $menu_link->getTitle();
    echo "Checking link: " . $title . "\n";
    
    // Check if this link has the menu-link-promocje class
    $link_options = $menu_link->link->first()->options;
    
    if (isset($link_options['attributes']['class']) && 
        in_array('menu-link-promocje', $link_options['attributes']['class'])) {
      
      echo "Found link with menu-link-promocje class: " . $title . "\n";
      
      // Update the menu link
      $menu_link->set('title', 'Voucher na szkolenia');
      $menu_link->set('link', [
        'uri' => 'https://4grow.pl/voucher-szkoleniowy',
        'options' => $link_options // Keep existing options including class
      ]);
      
      $menu_link->save();
      
      echo "Updated menu link to 'Voucher na szkolenia'\n";
      break; // Found and updated, exit loop
    }
  }
}

echo "Script completed.\n";
